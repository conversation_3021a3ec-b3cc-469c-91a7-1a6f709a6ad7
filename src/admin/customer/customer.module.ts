import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { Customer } from './customer.entity';
import { CategoryModule } from '../category/category.module';
import { AppointmentModule } from '../appointment/appointment.module';
import { RfidModule } from '../rfid/rfid.module';
import { Credit } from '../credit/credit.entity';
import { Appointment } from '../appointment/appointment.entity';
import { ProductModule } from '../product/product.module';
import { IssueCouponModule } from '../inventory/issue-coupon.module';
import { CreditHistory } from '../credit-history/credit-history.entity';
import { CustomerCreditImportService } from './services/customer-credit-import.service';
import { CreditSetting } from '../settings/credit/credit-setting.entity';
import { Branch } from '../branch/branch.entity';
import { Setting } from '../setting/setting.entity';
import { Product } from '../product/product.entity';
import { Category } from '../category/category.entity';
import { Order } from '../order/order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { Invoice } from '../invoice/invoice.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      Credit,
      Appointment,
      CreditHistory,
      CreditSetting,
      Branch,
      Setting,
      Product,
      Category,
      Order,
      OrderDetail,
      Invoice,
    ]),
    CategoryModule,
    AppointmentModule,
    RfidModule,
    ProductModule,
    IssueCouponModule,
  ],
  controllers: [CustomerController],
  providers: [CustomerService, CustomerCreditImportService],
  exports: [CustomerService, CustomerCreditImportService],
})
export class CustomerModule {}
