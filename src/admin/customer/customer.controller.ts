import {
  CrudController,
  CrudRequest,
  CrudRequestInterceptor,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';

import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Customer } from './customer.entity';
import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dto/createCustomer.dto';
import { UpdateCustomerDto } from './dto/updateCustomer.dto';
import {
  Body,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  UseInterceptors,
  UsePipes,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CategoryService } from '../category/category.service';
import { Public } from '../auth/decorators/public.decerator';
import { checkInCustomerDto } from './dto/checkInCustomer.dto';
import { RfidService } from '../rfid/rfid.service';
import { Appointment } from '../appointment/appointment.entity';
import * as jwt from 'jsonwebtoken';
import { CheckCouponCodeByTokenDto } from './dto/checkCouponCodeByToken.dto';
import {
  AppointmentSummaryQueryDto,
  InvoiceSummaryQueryDto,
  MembershipSummaryQueryDto,
} from './dto/summaryCustomer.dto';
import { CouponService } from '../product/coupon/coupon.service';
import { IssueCouponService } from '../inventory/issue-coupon.service';
import { CustomerNricPipe } from './validation/customerNric.pipe';
import { CustomerCreditImportService } from './services/customer-credit-import.service';
import { ImportCustomerCreditDto } from './dto/import-customer-credit.dto';

@BaseCrud(
  {
    model: {
      type: Customer,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreateCustomerDto,
      update: UpdateCustomerDto,
    },
    query: {
      join: {
        preferreds: { eager: true, allow: ['id', 'fullName', 'displayName'] },
        status: { eager: true, allow: ['id', 'name'] },
        gender: { eager: true, allow: ['id', 'name'] },
        nationality: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
        credits: {
          eager: true,
          allow: [
            'id',
            'creditBalance',
            'creditSetting',
            'issueDate',
            'expiryDate',
            'status',
          ],
        },
        'credits.creditSetting': {
          eager: true,
          allow: ['id', 'creditBalance', 'creditType'],
        },
      },
      filter: {
        $or: [
          { code: { $cont: '' } },
          {
            lastName: { $contInStr: '' },
          },
          { nric: { $contL: '' } },
          { phone: { $contL: '' } },
          { email: { $contL: '' } },
          { membershipNo: { $contL: '' } },
        ],
      },
      sort: [
        {
          field: 'created',
          order: 'DESC',
        },
      ],
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CustomerController extends BaseCrudController<Customer> {
  constructor(
    public service: CustomerService,
    private readonly categoryService: CategoryService,
    private readonly rfidService: RfidService,
    private readonly couponService: CouponService,
    private readonly issueCouponService: IssueCouponService,
    private readonly importService: CustomerCreditImportService,
  ) {
    super(service);
  }

  get base(): CrudController<Customer> {
    return this;
  }

  @Get('/:id/appointment')
  getAppointment(@Param('id') customerId: string): Promise<Appointment[]> {
    return this.service.getAppointmentInDay(customerId);
  }

  @Get(':id/coupon-can-use')
  async getListCouponItemByEmail(
    @Param('id') customerId: string,
    @Query() { isCombo }: { isCombo: string },
  ): Promise<any> {
    const customer = await this.service.findOne({ where: { id: customerId } });
    if (!customer) {
      throw new NotFoundException('not found customer!');
    }
    if (!customer.email) {
      return [];
    }
    return this.issueCouponService.getCouponItemListByEmail(
      customer.email,
      isCombo === 'true',
    );
  }

  @Post(':id/coupon-can-use')
  async getCouponItemByCode(
    @Param('id') customerId: string,
    @Body() { code }: { code: string },
  ): Promise<any> {
    const customer = await this.service.findOne({ where: { id: customerId } });
    if (!customer) {
      throw new NotFoundException('not found customer!');
    }
    if (!customer.email) {
      return null;
    }
    return this.issueCouponService.getCouponItemByEmailAndCode(code);
  }

  @Get('/:id/summary')
  getCustomerSummary(@Param('id') customerId: string): Promise<any> {
    return this.service.getCustomerSummary(customerId);
  }

  @Get('/:id/appointment/summary')
  getAppointmentSummary(
    @Param('id') customerId: string,
    @Query() { limit, page }: AppointmentSummaryQueryDto,
    @ParsedRequest() crudReq: CrudRequest,
  ): Promise<any> {
    return this.service.getAppointmentSummary(
      customerId,
      { limit, page },
      crudReq,
    );
  }

  @Get('/:id/invoice/summary')
  getInvoiceSummary(
    @Param('id') customerId: string,
    @Query() { limit, page }: InvoiceSummaryQueryDto,
  ): Promise<any> {
    return this.service.getInvoiceSummary(customerId, { limit, page });
  }

  @Get('/:id/membership/summary')
  getMembershipSummary(
    @Param('id') customerId: string,
    @Query() { creditType }: MembershipSummaryQueryDto,
    @ParsedRequest() crudReq: CrudRequest,
  ): Promise<any> {
    return this.service.getMembershipSummary(
      customerId,
      { creditType },
      crudReq,
    );
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Public()
  @Get('order')
  getOrder(@Query() { token }: { token: string }) {
    return this.rfidService.findOneByToken(token);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Public()
  @Get('menu')
  async getMenu(
    @ParsedRequest() req: CrudRequest,
    @Query() { token }: { token: string },
  ) {
    //verify token
    const payload = await jwt.verify(token, '0RdBHlbG8P1FFh6');
    const rfid = await this.rfidService.findOne({
      where: { token },
      relations: ['branch'],
    });
    req.parsed.filter = [
      // {
      //   field: 'name',
      //   operator: '$in',
      //   value: ['FOODBEVERAGE'],
      // },
      {
        field: 'name',
        operator: '$in',
        value: ['FOOD', 'BEVERAGE'],
      },
      {
        field: 'isMember',
        operator: '$eq',
        value: payload['isMember'] === true ? 'true' : 'false',
      },
    ];
    req.parsed.join = [
      {
        field: 'items',
        select: undefined,
      },
    ];
    return this.categoryService.getManyCategory(req, [rfid.branch.id]);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Post('check-in')
  checkIn(@ParsedRequest() req: CrudRequest, @Body() dto: checkInCustomerDto) {
    return this.service.checkIn(dto);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Post('check-out')
  checkOut(@Body('rfid') rfid: string) {
    return this.service.checkOut(rfid);
  }

  @Public()
  @Post('coupon/check-code')
  async checkCouponCode(
    @Body() data: CheckCouponCodeByTokenDto,
    @Req() req: Request,
  ): Promise<any> {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return await this.couponService.checkCouponCode(branchIds, data);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Get('check-in-list')
  async GetCustomerCheckInList(
    @Req() req: Request,
    @Query('keySearch') keySearch: string,
  ) {
    return this.service.checkInList(
      req.headers?.['branchid']?.split(',') || [],
      keySearch,
    );
  }

  @Override('createOneBase')
  @UsePipes(CustomerNricPipe)
  async createOneOverride(
    @Body() body: CreateCustomerDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ) {
    return this.service.createOne(crudRequest, body);
  }

  @Override('updateOneBase')
  @UsePipes(CustomerNricPipe)
  async updateOneOverride(
    @Body() body: UpdateCustomerDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ) {
    return this.service.updateOne(crudRequest, body);
  }

  @Override('deleteOneBase')
  deleteOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req,
    @Param('id') id: string,
  ) {
    return this.service.deleteOneCustomer(crudRequest, req, id);
  }

  @Post('import/credit')
  @UseInterceptors(FileInterceptor('file'))
  async importCustomerCredit(
    @UploadedFile() file: Express.Multer.File,
    @Body() options: ImportCustomerCreditDto,
  ) {
    if (!file) {
      throw new BadRequestException('CSV file is required');
    }

    if (!file.originalname.toLowerCase().endsWith('.csv')) {
      throw new BadRequestException('Only CSV files are allowed');
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      throw new BadRequestException('File size must be less than 10MB');
    }

    try {
      const result = await this.importService.importFromCSV(
        file.buffer,
        options,
      );
      return result;
    } catch (error) {
      throw new BadRequestException(`Import failed: ${error.message}`);
    }
  }

  @Post('import/credit/test-row')
  async testSingleRowImport(
    @Body() body: { csvRow: string; options?: ImportCustomerCreditDto },
  ) {
    if (!body.csvRow) {
      throw new BadRequestException('CSV row data is required');
    }

    try {
      return await this.importService.testSingleRow(
        body.csvRow,
        body.options || {},
      );
    } catch (error) {
      throw new BadRequestException(`Test failed: ${error.message}`);
    }
  }
}
