import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { Customer } from '../customer.entity';
import { Credit } from '../../credit/credit.entity';
import { CreditHistory } from '../../credit-history/credit-history.entity';
import { CreditSetting } from '../../settings/credit/credit-setting.entity';
import { Branch } from '../../branch/branch.entity';
import { Setting } from '../../setting/setting.entity';
import { Product } from '../../product/product.entity';
import { Category } from '../../category/category.entity';
import { Order } from '../../order/order.entity';
import { OrderDetail } from '../../order-detail/order-detail.entity';
import { Invoice } from '../../invoice/invoice.entity';

import {
  CustomerCreditCSVParser,
  ParsedCustomerCredit,
  ParseResult,
  AdditionalMembership,
} from '../utils/csv-parser.util';
import {
  ImportCustomerCreditDto,
  ImportResult,
  ImportError,
  ImportWarning,
} from '../dto/import-customer-credit.dto';
import {
  CreditStatus,
  CreditType,
  ProductType,
  OrderType,
  InvoiceStatus,
} from 'src/core/enums/entity';
import * as moment from 'moment-timezone';

@Injectable()
export class CustomerCreditImportService {
  private readonly logger = new Logger(CustomerCreditImportService.name);

  constructor(
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Credit)
    private creditRepo: Repository<Credit>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
    @InjectRepository(CreditSetting)
    private creditSettingRepo: Repository<CreditSetting>,
    @InjectRepository(Branch)
    private branchRepo: Repository<Branch>,
    @InjectRepository(Setting)
    private settingRepo: Repository<Setting>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    @InjectRepository(Category)
    private categoryRepo: Repository<Category>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    private dataSource: DataSource,
  ) {}

  /**
   * Import customer credit data from CSV buffer
   */
  async importFromCSV(
    buffer: Buffer,
    options: ImportCustomerCreditDto = {},
  ): Promise<ImportResult> {
    this.logger.log('Starting customer credit import process');

    try {
      // Parse CSV
      const parseResult: ParseResult = await CustomerCreditCSVParser.parseCSV(
        buffer,
      );

      if (parseResult.errors.length > 0 && !options.skipErrors) {
        return {
          success: false,
          message: 'CSV parsing failed with validation errors',
          summary: {
            totalRows: parseResult.totalRows,
            processedRows: 0,
            createdCustomers: 0,
            updatedCustomers: 0,
            createdCredits: 0,
            skippedRows: parseResult.totalRows,
            errorRows: parseResult.errors.length,
          },
          errors: parseResult.errors.map((err) => ({
            row: err.row,
            type: 'validation' as const,
            field: err.field,
            message: err.message,
            value: err.value,
          })),
          warnings: [],
        };
      }

      // Process valid data
      return await this.processImportData(parseResult.data, options);
    } catch (error) {
      this.logger.error('Import process failed', error);
      return {
        success: false,
        message: `Import failed: ${error.message}`,
        summary: {
          totalRows: 0,
          processedRows: 0,
          createdCustomers: 0,
          updatedCustomers: 0,
          createdCredits: 0,
          skippedRows: 0,
          errorRows: 1,
        },
        errors: [
          {
            row: 0,
            type: 'database',
            message: error.message,
          },
        ],
        warnings: [],
      };
    }
  }

  /**
   * Process parsed import data
   */
  private async processImportData(
    data: ParsedCustomerCredit[],
    options: ImportCustomerCreditDto,
  ): Promise<ImportResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const result: ImportResult = {
      success: true,
      message: 'Import completed successfully',
      summary: {
        totalRows: data.length,
        processedRows: 0,
        createdCustomers: 0,
        updatedCustomers: 0,
        createdCredits: 0,
        skippedRows: 0,
        errorRows: 0,
      },
      errors: [],
      warnings: [],
    };

    try {
      // Pre-load reference data
      const branches = await this.branchRepo.find();
      const creditSettings = await this.creditSettingRepo.find();
      const activeStatus = await this.settingRepo.findOne({
        where: { name: 'Active' },
      });
      let defaultCategory = await this.categoryRepo.findOne({
        where: { name: 'Membership' },
      });

      if (!defaultCategory) {
        // Fallback to first available category
        const categories = await this.categoryRepo.find({
          order: { id: 'ASC' },
          take: 1,
        });
        defaultCategory = categories[0] || null;
      }

      // Create branch map using both code and name for lookup
      const branchMap = new Map<string, Branch>();
      branches.forEach((b) => {
        if (b.code) {
          branchMap.set(b.code, b);
        }
        if (b.name) {
          branchMap.set(b.name, b);
        }
      });
      const newCreditSetting = creditSettings.find(
        (cs) => cs.creditType === 'credits',
      );

      if (!newCreditSetting) {
        throw new Error('Credit setting for "credits" type not found');
      }
      if (!defaultCategory) {
        throw new Error('No category found for membership products');
      }

      // Process each row with batch commits to prevent timeouts
      const BATCH_SIZE = 50; // Process 50 rows at a time

      for (let i = 0; i < data.length; i++) {
        const rowData = data[i];
        const rowNumber = i + 1;

        try {
          this.logger.log(
            `Processing row ${rowNumber}: Customer ${rowData.customerCodeOriginal} - ${rowData.firstName} ${rowData.lastName}`,
          );
          await this.processRow(
            queryRunner,
            rowData,
            rowNumber,
            options,
            branchMap,
            newCreditSetting,
            defaultCategory,
            activeStatus,
            result,
          );
          result.summary.processedRows++;
          this.logger.log(`Successfully processed row ${rowNumber}`);

          // Commit batch every BATCH_SIZE rows to prevent timeouts
          if (rowNumber % BATCH_SIZE === 0) {
            await queryRunner.commitTransaction();
            await queryRunner.startTransaction();
            this.logger.log(`Committed batch at row ${rowNumber}`);
          }
        } catch (error) {
          this.logger.error(
            `Error processing row ${rowNumber}: Customer ${rowData.customerCodeOriginal}`,
            error,
          );
          this.logger.error(`Row data:`, JSON.stringify(rowData, null, 2));
          this.logger.error(`Error stack:`, error.stack);
          result.errors.push({
            row: rowNumber,
            type: 'database',
            message: error.message,
          });
          result.summary.errorRows++;

          if (!options.skipErrors) {
            throw error;
          }
        }
      }

      await queryRunner.commitTransaction();
      this.logger.log(
        `Import completed: ${result.summary.processedRows} rows processed`,
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      result.success = false;
      result.message = `Import failed: ${error.message}`;
      throw error;
    } finally {
      await queryRunner.release();
    }

    return result;
  }

  /**
   * Process individual row
   */
  private async processRow(
    queryRunner: QueryRunner,
    data: ParsedCustomerCredit,
    rowNumber: number,
    options: ImportCustomerCreditDto,
    branchMap: Map<string, Branch>,
    newCreditSetting: CreditSetting,
    defaultCategory: Category,
    activeStatus: Setting,
    result: ImportResult,
  ): Promise<void> {
    // Find or create customer
    let customer = await queryRunner.manager.findOne(Customer, {
      where: { code: data.customerCode },
    });

    let isNewCustomer = false;
    if (!customer) {
      if (!options.createNew) {
        result.warnings.push({
          row: rowNumber,
          type: 'data_mismatch',
          message: `Customer ${data.customerCode} not found and createNew is disabled`,
        });
        result.summary.skippedRows++;
        return;
      }

      try {
        customer = await this.createCustomer(queryRunner, data, activeStatus);
        isNewCustomer = true;
        result.summary.createdCustomers++;
      } catch (error) {
        // Handle duplicate customer code error
        if (error.code === '23505' && error.detail?.includes('code')) {
          this.logger.warn(
            `Customer code ${data.customerCode} (${data.customerCodeOriginal}) already exists, attempting to find existing customer`,
          );
          customer = await queryRunner.manager.findOne(Customer, {
            where: { code: data.customerCode },
          });
          if (!customer) {
            throw new Error(
              `Customer code ${data.customerCode} (${data.customerCodeOriginal}) conflicts with existing customer but cannot be found`,
            );
          }
        } else {
          throw error;
        }
      }
    } else {
      if (options.updateExisting) {
        await this.updateCustomer(queryRunner, customer, data);
        result.summary.updatedCustomers++;
      }
    }

    // Find branch
    const branch = branchMap.get(data.branchCode);
    if (!branch) {
      if (options.defaultBranchId) {
        const defaultBranch = await queryRunner.manager.findOne(Branch, {
          where: { id: options.defaultBranchId },
        });
        if (!defaultBranch) {
          throw new Error(
            `Default branch ${options.defaultBranchId} not found`,
          );
        }
      } else {
        throw new Error(`Branch ${data.branchCode} not found`);
      }
    }

    // Create or find membership product
    const membershipProduct = await this.createOrFindMembershipProduct(
      queryRunner,
      data,
      defaultCategory,
      activeStatus,
    );

    // Create order and invoice to simulate membership purchase
    await this.createMembershipPurchase(
      queryRunner,
      customer,
      membershipProduct,
      data,
      branch || null,
      newCreditSetting,
      activeStatus,
    );

    result.summary.createdCredits++;

    // Process additional memberships if any
    if (data.additionalMemberships && data.additionalMemberships.length > 0) {
      for (const additionalMembership of data.additionalMemberships) {
        try {
          await this.processAdditionalMembership(
            queryRunner,
            customer,
            additionalMembership,
            branch || null,
            newCreditSetting,
            defaultCategory,
            activeStatus,
            result,
          );
        } catch (error) {
          this.logger.warn(
            `Failed to process additional membership for customer ${data.customerCode}: ${error.message}`,
          );
          // Continue processing other additional memberships
        }
      }
    }
  }

  /**
   * Process additional membership for existing customer
   */
  private async processAdditionalMembership(
    queryRunner: QueryRunner,
    customer: Customer,
    additionalMembership: AdditionalMembership,
    branch: Branch | null,
    newCreditSetting: CreditSetting,
    defaultCategory: Category,
    activeStatus: Setting,
    result: ImportResult,
  ): Promise<void> {
    // Create a temporary ParsedCustomerCredit object for the additional membership
    const tempData: ParsedCustomerCredit = {
      customerCode: customer.code,
      customerCodeOriginal: customer.code.toString(),
      firstName: customer.firstName,
      lastName: customer.lastName,
      membershipNo: customer.membershipNo,
      membershipType: '', // Will be derived from credit setting name
      expiryDate: customer.expiryDate,
      creditSettingName: additionalMembership.creditSettingName,
      creditBalance: additionalMembership.creditBalance,
      creditTotal: additionalMembership.creditTotal,
      branchCode: branch?.code || 'HQ',
      originalRow: additionalMembership.originalRow,
    };

    // Create or find membership product for additional membership
    const membershipProduct = await this.createOrFindMembershipProduct(
      queryRunner,
      tempData,
      defaultCategory,
      activeStatus,
    );

    // Create order and invoice for additional membership
    await this.createMembershipPurchase(
      queryRunner,
      customer,
      membershipProduct,
      tempData,
      branch,
      newCreditSetting,
      activeStatus,
    );

    result.summary.createdCredits++;
  }

  /**
   * Create new customer
   */
  private async createCustomer(
    queryRunner: QueryRunner,
    data: ParsedCustomerCredit,
    activeStatus: Setting,
  ): Promise<Customer> {
    const customer = queryRunner.manager.create(Customer, {
      code: data.customerCode,
      firstName: data.firstName,
      lastName: data.lastName,
      membershipNo: data.membershipNo,
      expiryDate: data.expiryDate,
      status: activeStatus,
    });

    return await queryRunner.manager.save(Customer, customer);
  }

  /**
   * Update existing customer
   */
  private async updateCustomer(
    queryRunner: QueryRunner,
    customer: Customer,
    data: ParsedCustomerCredit,
  ): Promise<void> {
    // Update fields if they're different
    let hasChanges = false;

    if (customer.firstName !== data.firstName) {
      customer.firstName = data.firstName;
      hasChanges = true;
    }

    if (customer.lastName !== data.lastName) {
      customer.lastName = data.lastName;
      hasChanges = true;
    }

    if (data.membershipNo && customer.membershipNo !== data.membershipNo) {
      customer.membershipNo = data.membershipNo;
      hasChanges = true;
    }

    if (data.expiryDate && customer.expiryDate !== data.expiryDate) {
      customer.expiryDate = data.expiryDate;
      hasChanges = true;
    }

    if (hasChanges) {
      await queryRunner.manager.save(Customer, customer);
    }
  }

  /**
   * Create or find membership product
   */
  private async createOrFindMembershipProduct(
    queryRunner: QueryRunner,
    data: ParsedCustomerCredit,
    defaultCategory: Category,
    activeStatus: Setting,
  ): Promise<Product> {
    // Try to find existing membership product
    let product = await queryRunner.manager.findOne(Product, {
      where: {
        name: data.creditSettingName,
        type: ProductType.MEMBERSHIP,
      },
    });

    if (!product) {
      // Create new membership product
      product = queryRunner.manager.create(Product, {
        name: data.creditSettingName,
        type: ProductType.MEMBERSHIP,
        price: data.creditTotal,
        credit: data.creditTotal,
        creditType: 'credits' as any,
        category: defaultCategory,
        status: activeStatus,
        periodUnit: 'month' as any,
        period: 12, // 1 year default
        isMember: true,
      });

      product = await queryRunner.manager.save(Product, product);
    }

    return product;
  }

  /**
   * Create membership purchase (order + invoice) to trigger credit creation
   */
  private async createMembershipPurchase(
    queryRunner: QueryRunner,
    customer: Customer,
    product: Product,
    data: ParsedCustomerCredit,
    branch: Branch | null,
    creditSetting: CreditSetting,
    activeStatus: Setting,
  ): Promise<void> {
    const now = new Date();

    // Generate order code
    const orderCode = await this.generateOrderCode(queryRunner);

    // Create order
    const order = queryRunner.manager.create(Order, {
      code: orderCode,
      branch,
      orderType: OrderType.MEMBERSHIP,
      status: InvoiceStatus.PAID,
      total: data.creditTotal,
      subTotal: data.creditTotal,
      totalBeforeTax: data.creditTotal,
      tax: 0,
      isDraft: false,
      isComplete: true,
    });

    const savedOrder = await queryRunner.manager.save(Order, order);

    // Create order detail
    const orderDetail = queryRunner.manager.create(OrderDetail, {
      order: savedOrder,
      product,
      quantity: 1,
      price: data.creditTotal,
    });

    await queryRunner.manager.save(OrderDetail, orderDetail);

    // Generate invoice code
    const invoiceCode = await this.generateInvoiceCode(queryRunner);

    // Create invoice
    const invoice = queryRunner.manager.create(Invoice, {
      code: invoiceCode,
      customer,
      branch,
      status: InvoiceStatus.PAID,
      total: data.creditTotal,
      subTotal: data.creditTotal,
      totalBeforeTax: data.creditTotal,
      tax: 0,
      date: now,
    });

    const savedInvoice = await queryRunner.manager.save(Invoice, invoice);

    // Update order with invoice reference
    await queryRunner.manager.update(Order, savedOrder.id, {
      invoice: savedInvoice,
    });

    // Create credit manually (simulating invoice service logic)
    await this.createCreditFromMembership(
      queryRunner,
      customer,
      product,
      data,
      creditSetting,
      branch,
      savedInvoice,
      savedOrder,
    );
  }

  /**
   * Generate unique order code
   */
  private async generateOrderCode(queryRunner: QueryRunner): Promise<number> {
    // Use a timestamp-based approach to generate unique codes
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 1000);
    let nextCode = parseInt(`${timestamp}${randomSuffix}`.slice(-9)); // Take last 9 digits to keep it reasonable

    // Ensure it's at least 6 digits
    if (nextCode < 100000) {
      nextCode = 100000 + randomSuffix;
    }

    // Check if this code already exists
    let attempts = 0;
    while (attempts < 10) {
      const existingOrder = await queryRunner.manager.findOne(Order, {
        where: { code: nextCode },
      });

      if (!existingOrder) {
        return nextCode;
      }

      // If code exists, increment and try again
      nextCode++;
      attempts++;
    }

    // Fallback: use a simple incremental approach with a different starting point
    const fallbackStart = 500000 + Math.floor(Math.random() * 100000);
    for (let code = fallbackStart; code < fallbackStart + 10000; code++) {
      const existing = await queryRunner.manager.findOne(Order, {
        where: { code },
      });
      if (!existing) {
        return code;
      }
    }

    throw new Error('Unable to generate unique order code');
  }

  /**
   * Generate unique invoice code
   */
  private async generateInvoiceCode(queryRunner: QueryRunner): Promise<string> {
    const lastInvoice = await queryRunner.manager
      .createQueryBuilder(Invoice, 'invoice')
      .where("invoice.code ~ '^[0-9]+$'") // Only numeric codes
      .orderBy('CAST(invoice.code AS INTEGER)', 'DESC')
      .getOne();

    let nextNumber = 100000; // Default starting number
    if (lastInvoice && lastInvoice.code) {
      const lastNumber = parseInt(lastInvoice.code);
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }

    return nextNumber.toString();
  }

  /**
   * Create credit from membership purchase (simulating invoice service logic)
   */
  private async createCreditFromMembership(
    queryRunner: QueryRunner,
    customer: Customer,
    product: Product,
    data: ParsedCustomerCredit,
    creditSetting: CreditSetting,
    branch: Branch | null,
    invoice: Invoice,
    order: Order,
  ): Promise<void> {
    const now = new Date();
    const expiryDate = data.expiryDate || moment().add(1, 'year').toDate();

    // Create credit
    const credit = queryRunner.manager.create(Credit, {
      customer,
      creditSetting,
      branch,
      issueDate: now,
      expiryDate,
      total: data.creditTotal,
      creditBalance: data.creditBalance,
      status: CreditStatus.VALID,
    });

    const savedCredit = await queryRunner.manager.save(Credit, credit);

    // Get the order details for detail
    const orderDetails = await queryRunner.manager.find(OrderDetail, {
      where: { order: { id: order.id } },
      relations: ['product'],
    });

    // Create credit history (simulating invoice service logic)
    const creditHistory = queryRunner.manager.create(CreditHistory, {
      credit: savedCredit,
      customer,
      product,
      invoice,
      paid: data.creditTotal,
      opening: 0,
      closing: data.creditBalance,
      usable: data.creditBalance,
      expiryDate,
      isMembershipPkg: true,
      detail: {
        id: invoice.id,
        code: invoice.code,
        date: now,
        orders: [
          {
            id: order.id,
            items: orderDetails.map((item) => ({
              id: item.id,
              product: {
                id: item.product.id,
                name: item.product.name,
                price: item.product.price,
                credit: item.product.credit,
              },
              quantity: item.quantity,
              price: item.price,
            })),
          },
        ],
      },
    });

    await queryRunner.manager.save(CreditHistory, creditHistory);
  }

  /**
   * Test import with a single row for debugging
   */
  async testSingleRow(
    csvRow: string,
    options: ImportCustomerCreditDto = {},
  ): Promise<ImportResult> {
    this.logger.log('Testing single row import');

    // Create a minimal CSV with header and the test row
    const csvContent = `#,Customer,Group,Credit,Balance,Total,Branch\n${csvRow}`;
    const buffer = Buffer.from(csvContent, 'utf-8');

    try {
      // Parse the single row
      const parseResult = await CustomerCreditCSVParser.parseCSV(buffer);

      if (parseResult.errors.length > 0) {
        return {
          success: false,
          message: 'CSV parsing failed with validation errors',
          summary: {
            totalRows: 1,
            processedRows: 0,
            createdCustomers: 0,
            updatedCustomers: 0,
            createdCredits: 0,
            skippedRows: 1,
            errorRows: parseResult.errors.length,
          },
          errors: parseResult.errors.map((err) => ({
            row: err.row,
            type: 'validation' as const,
            field: err.field,
            message: err.message,
            value: err.value,
          })),
          warnings: [],
        };
      }

      // Process the single row with skipErrors enabled
      const testOptions = { ...options, skipErrors: true };
      return await this.processImportData(parseResult.data, testOptions);
    } catch (error) {
      this.logger.error('Single row test failed', error);
      return {
        success: false,
        message: `Single row test failed: ${error.message}`,
        summary: {
          totalRows: 1,
          processedRows: 0,
          createdCustomers: 0,
          updatedCustomers: 0,
          createdCredits: 0,
          skippedRows: 0,
          errorRows: 1,
        },
        errors: [
          {
            row: 1,
            type: 'database',
            message: error.message,
          },
        ],
        warnings: [],
      };
    }
  }
}
