import * as csv from 'csv-parser';
import { Readable } from 'stream';

export interface CustomerCreditRow {
  index: string;
  customer: string;
  group: string;
  credit: string;
  balance: string;
  total: string;
  branch: string;
}

export interface ParsedCustomerCredit {
  customerCode: number;
  customerCodeOriginal: string; // Keep original for reference
  firstName: string;
  lastName: string;
  membershipNo?: string;
  membershipType: string;
  expiryDate?: Date;
  creditSettingName: string;
  creditBalance: number;
  creditTotal: number;
  branchCode: string;
  originalRow: CustomerCreditRow;
  additionalMemberships?: AdditionalMembership[]; // Additional memberships for the same customer
}

export interface AdditionalMembership {
  creditSettingName: string;
  creditBalance: number;
  creditTotal: number;
  originalRow: CustomerCreditRow;
}

export interface ValidationError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ParseResult {
  data: ParsedCustomerCredit[];
  errors: ValidationError[];
  totalRows: number;
  validRows: number;
}

export class CustomerCreditCSVParser {
  /**
   * Branch code mapping for legacy CSV files
   */
  private static readonly BRANCH_CODE_MAPPING: Record<string, string> = {
    HQ: 'SINGAPORE',
    // Add more mappings as needed
  };

  /**
   * Convert customer code string to number
   * For numeric codes: return as-is
   * For alphanumeric codes: create a hash-based number
   */
  private static convertCustomerCodeToNumber(codeStr: string): number {
    // If it's already a number, return it
    if (/^\d+$/.test(codeStr)) {
      return parseInt(codeStr);
    }

    // For alphanumeric codes like CS52262, create a hash
    let hash = 0;
    for (let i = 0; i < codeStr.length; i++) {
      const char = codeStr.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Ensure positive number and add prefix to avoid collision with existing numeric codes
    return Math.abs(hash) + 9000000; // Start from 9M to avoid collision with existing codes
  }

  /**
   * Parse CSV buffer to customer credit data
   */
  static async parseCSV(buffer: Buffer): Promise<ParseResult> {
    const results: CustomerCreditRow[] = [];
    const errors: ValidationError[] = [];

    return new Promise((resolve, reject) => {
      const stream = Readable.from(buffer);

      stream
        .pipe(
          csv({
            headers: [
              'index',
              'customer',
              'group',
              'credit',
              'balance',
              'total',
              'branch',
            ],
          }),
        )
        .on('data', (row: CustomerCreditRow) => {
          // Skip header row (handle BOM character and various header formats)
          if (
            row.index === '#' ||
            row.index === 'index' ||
            row.index === '﻿#' ||
            row.customer === 'Customer' ||
            row.customer?.toLowerCase().includes('customer')
          ) {
            return;
          }
          results.push(row);
        })
        .on('end', () => {
          const parseResult = this.processRows(results);
          resolve(parseResult);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  /**
   * Process raw CSV rows and validate data
   */
  private static processRows(rows: CustomerCreditRow[]): ParseResult {
    const data: ParsedCustomerCredit[] = [];
    const errors: ValidationError[] = [];
    let currentCustomer: ParsedCustomerCredit | null = null;

    rows.forEach((row, index) => {
      try {
        // Check if this is an additional membership row (starts with empty customer field)
        if (!row.customer || row.customer.trim() === '') {
          if (currentCustomer && row.credit && row.balance) {
            // Parse additional membership data
            const additionalMembership = this.parseAdditionalMembership(
              row,
              index + 1,
            );
            if (additionalMembership) {
              if (!currentCustomer.additionalMemberships) {
                currentCustomer.additionalMemberships = [];
              }
              currentCustomer.additionalMemberships.push(additionalMembership);
            }
          }
          // Skip validation for additional membership rows
          return;
        }

        // Parse main customer row
        const parsed = this.parseRow(row, index + 1);
        const validationErrors = this.validateRow(parsed, index + 1);

        if (validationErrors.length === 0) {
          data.push(parsed);
          currentCustomer = parsed; // Set as current customer for additional memberships
        } else {
          errors.push(...validationErrors);
          currentCustomer = null; // Reset current customer on validation error
        }
      } catch (error) {
        errors.push({
          row: index + 1,
          field: 'general',
          message: `Failed to parse row: ${error.message}`,
          value: row,
        });
        currentCustomer = null; // Reset current customer on parse error
      }
    });

    return {
      data,
      errors,
      totalRows: rows.length,
      validRows: data.length,
    };
  }

  /**
   * Parse additional membership row (rows with empty customer field)
   */
  private static parseAdditionalMembership(
    row: CustomerCreditRow,
    rowNumber: number,
  ): AdditionalMembership | null {
    try {
      // Parse credit setting name
      const creditSettingName = row.credit
        .replace(/"/g, '')
        .replace(/^\$/, '')
        .trim();

      // Parse balance: "378.46 / 550.00"
      const balanceMatch = row.balance.match(
        /^"?([0-9.]+)\s*\/\s*([0-9.]+)"?$/,
      );
      if (!balanceMatch) {
        console.warn(
          `Invalid balance format in additional membership row ${rowNumber}: ${row.balance}`,
        );
        return null;
      }

      const creditBalance = parseFloat(balanceMatch[1]);
      const creditTotal = parseFloat(balanceMatch[2]);

      return {
        creditSettingName,
        creditBalance,
        creditTotal,
        originalRow: row,
      };
    } catch (error) {
      console.warn(
        `Failed to parse additional membership row ${rowNumber}: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Parse individual row
   */
  private static parseRow(
    row: CustomerCreditRow,
    rowNumber: number,
  ): ParsedCustomerCredit {
    // Skip rows with empty customer field (these might be additional memberships for same customer)
    if (!row.customer || row.customer.trim() === '') {
      throw new Error('Customer field is empty - skipping row');
    }

    // Parse customer field: "100042: KAILING TAN GC009671" or "CS52262: SEE WOON SAN"
    const customerMatch = row.customer.match(
      /^"?([A-Z0-9]+):\s*([^"]+?)\s*([A-Z]{2}\d+)?"?$/,
    );
    if (!customerMatch) {
      throw new Error(`Invalid customer format: ${row.customer}`);
    }

    const customerCodeOriginal = customerMatch[1];
    const customerCode = this.convertCustomerCodeToNumber(customerCodeOriginal);

    const fullName = customerMatch[2].trim();
    const membershipNo = customerMatch[3] || undefined;

    // Split name into first and last name
    const nameParts = fullName.split(' ');
    const firstName = nameParts[0];
    const lastName =
      nameParts.length > 1 ? nameParts.slice(1).join(' ') : firstName; // Use firstName as lastName if only one name

    // Parse group field: "Member (Expr. 18-03-2026)"
    let membershipType = '';
    let expiryDate: Date | undefined;

    if (row.group) {
      const groupMatch = row.group.match(
        /^"?([^(]+?)(?:\s*\(Expr\.\s*(\d{2}-\d{2}-\d{4})\))?"?$/,
      );
      if (groupMatch) {
        membershipType = groupMatch[1].trim();
        if (groupMatch[2]) {
          // Parse date in DD-MM-YYYY format
          const [day, month, year] = groupMatch[2].split('-');
          expiryDate = new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
          );
        }
      } else {
        membershipType = row.group.replace(/"/g, '').trim();
      }
    }

    // Parse credit setting name
    const creditSettingName = row.credit
      .replace(/"/g, '')
      .replace(/^\$/, '')
      .trim();

    // Parse balance: "378.46 / 550.00"
    const balanceMatch = row.balance.match(/^"?([0-9.]+)\s*\/\s*([0-9.]+)"?$/);
    if (!balanceMatch) {
      throw new Error(`Invalid balance format: ${row.balance}`);
    }

    const creditBalance = parseFloat(balanceMatch[1]);
    const creditTotal = parseFloat(balanceMatch[2]);

    // Verify with total field
    const totalFromField = parseFloat(row.total);
    if (
      !isNaN(totalFromField) &&
      Math.abs(creditBalance - totalFromField) > 0.01
    ) {
      console.warn(
        `Balance mismatch: balance field ${creditBalance} vs total field ${totalFromField} (Row: ${rowNumber})`,
      );
    }

    // Parse branch: "[HQ]"
    const branchMatch = row.branch.match(/^\[([^\]]+)\]$/);
    if (!branchMatch) {
      throw new Error(`Invalid branch format: ${row.branch}`);
    }
    let branchCode = branchMatch[1];

    // Apply branch code mapping if exists
    if (this.BRANCH_CODE_MAPPING[branchCode]) {
      branchCode = this.BRANCH_CODE_MAPPING[branchCode];
    }

    return {
      customerCode,
      customerCodeOriginal,
      firstName,
      lastName,
      membershipNo,
      membershipType,
      expiryDate,
      creditSettingName,
      creditBalance,
      creditTotal,
      branchCode,
      originalRow: row,
    };
  }

  /**
   * Validate parsed row data
   */
  private static validateRow(
    data: ParsedCustomerCredit,
    rowNumber: number,
  ): ValidationError[] {
    const errors: ValidationError[] = [];

    // Validate customer code
    if (!data.customerCode || data.customerCode <= 0) {
      errors.push({
        row: rowNumber,
        field: 'customerCode',
        message: 'Customer code is required and must be positive',
        value: data.customerCode,
      });
    }

    // Validate names
    if (!data.firstName || data.firstName.length < 1) {
      errors.push({
        row: rowNumber,
        field: 'firstName',
        message: 'First name is required',
        value: data.firstName,
      });
    }

    if (!data.lastName || data.lastName.length < 1) {
      errors.push({
        row: rowNumber,
        field: 'lastName',
        message: 'Last name is required',
        value: data.lastName,
      });
    }

    // Validate credit amounts
    if (isNaN(data.creditBalance) || data.creditBalance < 0) {
      errors.push({
        row: rowNumber,
        field: 'creditBalance',
        message: 'Credit balance must be a valid positive number',
        value: data.creditBalance,
      });
    }

    if (isNaN(data.creditTotal) || data.creditTotal <= 0) {
      errors.push({
        row: rowNumber,
        field: 'creditTotal',
        message: 'Credit total must be a valid positive number',
        value: data.creditTotal,
      });
    }

    if (data.creditBalance > data.creditTotal) {
      errors.push({
        row: rowNumber,
        field: 'creditBalance',
        message: 'Credit balance cannot exceed credit total',
        value: `${data.creditBalance} > ${data.creditTotal}`,
      });
    }

    // Validate credit setting name
    if (!data.creditSettingName || data.creditSettingName.length < 3) {
      errors.push({
        row: rowNumber,
        field: 'creditSettingName',
        message:
          'Credit setting name is required and must be at least 3 characters',
        value: data.creditSettingName,
      });
    }

    // Validate branch code
    if (!data.branchCode || data.branchCode.length < 1) {
      errors.push({
        row: rowNumber,
        field: 'branchCode',
        message: 'Branch code is required',
        value: data.branchCode,
      });
    }

    // Handle expiry date in the past - set to 1 year from now instead of rejecting
    if (data.expiryDate && data.expiryDate < new Date()) {
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      data.expiryDate = oneYearFromNow;
    }

    return errors;
  }
}
