import {
  <PERSON>tity,
  Column,
  OneToMany,
  ManyToMany,
  JoinTable,
  ManyToOne,
  Index,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Employee } from '../employee/employee.entity';
import { Setting } from '../setting/setting.entity';
import { IsEmail, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { Media } from '../media/media.entity';
import { Credit } from '../credit/credit.entity';
@Entity('customer')
export class Customer extends DocEntity {
  @Column({ type: 'bigint', nullable: false })
  code: number;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  firstName: string;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  lastName: string;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  nric: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  @Index({ unique: true, where: 'deleted IS NULL' })
  phone?: string;

  @Column({
    nullable: true,
  })
  @Index({ unique: false, where: 'deleted IS NULL' })
  @ApiPropertyOptional()
  @IsEmail()
  email?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  @ApiPropertyOptional()
  address?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  @ApiPropertyOptional()
  remark?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  membershipNo?: string;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  expiryDate?: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  @ValidateNested()
  gender: Setting;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  firstVisit?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiProperty()
  birthDay: Date;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  referralSource?: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiPropertyOptional({ type: () => Setting })
  nationality?: Setting;

  @ApiProperty({ required: false })
  @OneToOne(() => Media, (media) => media.product)
  @JoinColumn()
  avatar: Media;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @ManyToMany(() => Employee)
  @ApiProperty({ type: () => Employee, isArray: true })
  @JoinTable()
  preferreds: Employee[];

  @OneToMany(() => Credit, (credit) => credit.customer)
  credits: Credit[];

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  passportUsageLimit?: number;

  @Column({ type: 'varchar', nullable: true, unique: true })
  legacyCode?: string;
}
