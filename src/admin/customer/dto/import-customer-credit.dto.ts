import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsBoolean, IsString } from 'class-validator';

export class ImportCustomerCreditDto {
  @ApiPropertyOptional({ 
    description: 'Whether to update existing customers if found',
    default: true 
  })
  @IsOptional()
  @IsBoolean()
  updateExisting?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Whether to create new customers if not found',
    default: true 
  })
  @IsOptional()
  @IsBoolean()
  createNew?: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Whether to skip validation errors and continue processing',
    default: false 
  })
  @IsOptional()
  @IsBoolean()
  skipErrors?: boolean = false;

  @ApiPropertyOptional({ 
    description: 'Default branch ID to use if branch not found',
  })
  @IsOptional()
  @IsString()
  defaultBranchId?: string;
}

export interface ImportResult {
  success: boolean;
  message: string;
  summary: {
    totalRows: number;
    processedRows: number;
    createdCustomers: number;
    updatedCustomers: number;
    createdCredits: number;
    skippedRows: number;
    errorRows: number;
  };
  errors: ImportError[];
  warnings: ImportWarning[];
}

export interface ImportError {
  row: number;
  type: 'validation' | 'database' | 'business_logic';
  field?: string;
  message: string;
  value?: any;
}

export interface ImportWarning {
  row: number;
  type: 'data_mismatch' | 'duplicate' | 'assumption';
  message: string;
  value?: any;
}

export interface CustomerImportData {
  customerCode: number;
  firstName: string;
  lastName: string;
  membershipNo?: string;
  expiryDate?: Date;
  creditSettingName: string;
  creditBalance: number;
  creditTotal: number;
  branchCode: string;
  membershipType: string;
}
