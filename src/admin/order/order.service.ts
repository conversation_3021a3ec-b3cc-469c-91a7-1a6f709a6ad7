import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import * as _ from 'lodash';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, IsNull, Not, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Order } from './order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { Invoice } from '../invoice/invoice.entity';
import { CreateOrderFromMobileDto } from './dto/createOrderFromMobile.dto';
import { Rfid } from '../rfid/rfid.entity';
import { Product } from '../product/product.entity';
import { Appointment } from '../appointment/appointment.entity';
import {
  AppointmentStatus,
  FbOrderStatus,
  InvoiceStatus,
  OrderType,
  ProductType,
} from '../../core/enums/entity';
import { FbOrder } from '../fb-order/fb-order.entity';
import { TransferFbOrder } from './dto/transferFbOrder.dto';
import { CouponItem } from '../inventory/coupon-item.entity';
import { AuditTrailOperation } from '../audit-trail/audit-trail.entity';
import { AuditTrailService } from '../audit-trail/audit-trail.service';
import { CrudRequest } from 'src/core/crud/crud';
import { User } from '../user/user.entity';
import * as moment from 'moment';
@Injectable()
export class OrderService extends BaseCrudService<Order> {
  constructor(
    @InjectRepository(Order) repo: Repository<Order>,
    @InjectRepository(Rfid) private rfidRepo: Repository<Rfid>,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
    private readonly auditTrailService: AuditTrailService,
  ) {
    super(repo);
  }

  async getListOrder(req: CrudRequest, keySearch: string) {
    const data: any = {
      isDraft: false,
    };

    if (req.parsed?.filter) {
      for (const f of req.parsed.filter) {
        if (f.field === 'created' && f.operator === '$between') {
          data.startTime = f.value[0];
          data.endTime = f.value[1];
        }

        if (f.field === 'isDraft' && f.operator === '$eq') {
          data.isDraft = f.value;
        }

        if (f.field === 'status' && f.operator === '$eq') {
          data.status = f.value;
        }
      }
    }

    const queryBuilder = this.repo
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.appointment', 'appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('customer.gender', 'gender')
      .leftJoinAndSelect('customer.avatar', 'avatar')
      .leftJoinAndSelect('customer.credits', 'credits')
      .leftJoinAndSelect('credits.creditSetting', 'creditSetting')
      .leftJoinAndSelect('order.items', 'items')
      .leftJoinAndSelect('items.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('product.avatar', 'productAvatar')
      .leftJoinAndSelect('items.employees', 'employees')
      .leftJoinAndSelect('appointment.rfids', 'rfids')
      .leftJoinAndSelect('rfids.group', 'group')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .where('order.isDraft = :isDraft', { isDraft: data.isDraft });

    // Only add time filter if startTime and endTime exist
    if (data.startTime && data.endTime) {
      queryBuilder.andWhere('(order.created BETWEEN :startTime and :endTime)', {
        startTime: data.startTime,
        endTime: data.endTime,
      });
    }

    if (data.status) {
      queryBuilder.andWhere('order.status = :status', {
        status: data.status,
      });
    }

    queryBuilder.andWhere('appointment.rfid IS NOT NULL');

    // Sort by order number (code) descending
    queryBuilder.orderBy('order.code', 'DESC');

    if (keySearch) {
      const isNumeric = /^[0-9]+$/.test(keySearch);

      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch')
            .orWhere('customer.lastName ILIKE :keySearch')
            .orWhere(
              "concat(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :keySearch",
            )
            .orWhere('CAST(invoice.code AS TEXT) ILIKE :keySearch');

          if (isNumeric) {
            qb.orWhere('CAST(appointment.rfid AS TEXT) = :exactRfid')
              .orWhere('CAST(appointment.rfid AS TEXT) LIKE :rfidPattern')
              .orWhere(
                "LPAD(CAST(rfids.lockerNumber AS TEXT), 3, '0') = :exactRfid",
              )
              .orWhere('rfids.lockerNumber = :lockerNumberNumeric');
          } else {
            qb.orWhere(
              'CAST(appointment.rfid AS TEXT) ILIKE :keySearch',
            ).orWhere(
              "LPAD(CAST(rfids.lockerNumber AS TEXT), 3, '0') ILIKE :keySearch",
            );
          }

          qb.orWhere('rfids.serialCode ILIKE :keySearch');
        }),
        {
          keySearch: `%${keySearch}%`,
          exactRfid: keySearch,
          rfidPattern: `%${keySearch}%`,
          lockerNumberNumeric: Number(keySearch),
        },
      );
    }

    const orders = await queryBuilder.getMany();
    await Promise.all(
      orders.map(async (order) => {
        if (order.invoice && typeof order.invoice.total !== 'undefined') {
          order.subTotal = order.invoice.total;
        }
      }),
    );
    return orders;
  }

  async createOneOrder(req: Request, dto: any) {
    let order = null;
    const couponName = null;
    //get branchId from appointment
    const appointment = await this.repo.manager.findOne(Appointment, {
      where: { id: dto.appointment.id },
      relations: ['branch'],
    });

    // If remark is provided, save it to order's note field
    if (dto.remark) {
      dto.note = dto.remark;
    }

    dto['branch'] = { id: appointment.branch?.id };
    const productArr = {
      membership: [],
      food_beverage: [],
      others: [],
    };
    const listProducts = dto.items;
    if (dto?.items) {
      for (const item of dto.items) {
        switch (item.product.type) {
          case ProductType.MEMBERSHIP:
            productArr['membership'].push(item);
            break;
          case ProductType.FOOD:
          case ProductType.BEVERAGE:
            productArr['food_beverage'].push(item);
            break;
          default:
            productArr['others'].push(item);
            break;
        }
      }
    }

    const additionalServices = [];

    for (const item of dto.items) {
      if (item.couponCode && item.product?.id) {
        const couponProductId = item.product.id;

        const assignedServices = await this.repo.manager.find(Product, {
          where: { parent: { id: couponProductId } },
          relations: [
            'category',
            'duration',
            'avatar',
            'branches',
            'status',
            'priceType',
            'parent',
          ],
        });

        for (const service of assignedServices) {
          additionalServices.push({
            product: {
              id: service.id,
              name: service.name,
              price: service.price,
              type: ProductType.SERVICE,
              category: service.category,
              duration: service.duration,
              status: service.status,
              priceType: service.status,
              branches: service.branches,
              parent: service.parent,
            },
            quantity: 1,
            couponCode: item.couponCode,
          });
        }
      }
    }

    if (additionalServices.length > 0) {
      productArr.others.push(...additionalServices);
    }

    delete dto.items;
    const orderIdArr = [];
    const currentOrder = await this.repo
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.items', 'items')
      .leftJoinAndSelect('items.duration', 'duration')
      .leftJoinAndSelect('items.product', 'product')
      .leftJoinAndSelect('items.employees', 'employees')
      .leftJoinAndSelect('product.category', 'category')
      .where('order.appointmentId = :appointmentId', {
        appointmentId: dto.appointment.id,
      })
      .andWhere(
        new Brackets((qb) => {
          qb.where('order.invoice IS NULL').orWhere('order.status = :status', {
            status: InvoiceStatus.PART_PAID,
          });
        }),
      )
      .getMany();
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      //Create audit trail log
      await this.auditTrailService.createRecordLog(queryRunner, {
        createdById: req?.['user']?.['id'],
        branchId: appointment.branch?.id,
        action: AuditTrailOperation.ORDER,
        payload: [],
        startTime: req?.['headers']?.['starttime'],
        newPayload: listProducts,
      });
      // Case: F&B
      if (productArr.food_beverage.length > 0) {
        // Create F&B order
        const foodItems = productArr.food_beverage.filter(
          (item) => item.product.type === ProductType.FOOD,
        );
        const beverageItems = productArr.food_beverage.filter(
          (item) => item.product.type === ProductType.BEVERAGE,
        );
        // Create F&B order
        if (foodItems.length) {
          const fbOrder = await queryRunner.manager.save(FbOrder, {
            ...dto,
            productType: ProductType.FOOD,
            orderType: OrderType.FOOD_BEVERAGE,
          });
          const fbOrderDto = { ...dto };
          fbOrderDto.items = foodItems;
          await this.processTaxForFbOrder(queryRunner, fbOrderDto, fbOrder);
        }
        if (beverageItems.length) {
          const fbOrder = await queryRunner.manager.save(FbOrder, {
            ...dto,
            productType: ProductType.BEVERAGE,
            orderType: OrderType.FOOD_BEVERAGE,
          });
          const fbOrderDto = { ...dto };
          fbOrderDto.items = beverageItems;
          await this.processTaxForFbOrder(queryRunner, fbOrderDto, fbOrder);
        }

        let idOfFoodBeverageOrder = null;
        const foodBeverageOrderItemIds = [];
        if (currentOrder.length > 0) {
          const foodBeverageOrder = currentOrder.find(
            (order) => order.orderType === OrderType.FOOD_BEVERAGE,
          );
          if (foodBeverageOrder) {
            idOfFoodBeverageOrder = foodBeverageOrder.id;
            if (
              foodBeverageOrder?.items &&
              foodBeverageOrder?.items?.length > 0
            ) {
              for (const item of foodBeverageOrder.items) {
                for (let i = 0; i < productArr.food_beverage.length; i++) {
                  const fbItem = productArr.food_beverage[i];
                  if (fbItem?.product?.id === item?.product?.id) {
                    item.quantity = item.quantity + fbItem.quantity;
                    productArr.food_beverage.splice(i, 1);
                    break;
                  }
                }
                productArr.food_beverage.push({
                  product: item?.product,
                  quantity: item.quantity,
                });
                foodBeverageOrderItemIds.push(item.id);
              }
            }
          }
        }
        const fBDataId = await this.processCreateOrder(
          queryRunner,
          dto,
          productArr.food_beverage,
          OrderType.FOOD_BEVERAGE,
          idOfFoodBeverageOrder,
          foodBeverageOrderItemIds,
        );
        orderIdArr.push(fBDataId);
      }
      // Case: membership
      if (productArr.membership.length > 0) {
        let idOfMembershipOrder = null;
        const membershipOrderItemIds = [];
        if (currentOrder.length > 0) {
          const membershipOrder = currentOrder.find(
            (order) => order.orderType === OrderType.MEMBERSHIP,
          );
          if (membershipOrder) {
            idOfMembershipOrder = membershipOrder.id;
            if (membershipOrder?.items && membershipOrder?.items?.length > 0) {
              for (const item of membershipOrder.items) {
                for (let i = 0; i < productArr.membership.length; i++) {
                  const membershipItem = productArr.membership[i];
                  if (membershipItem?.product?.id === item?.product?.id) {
                    item.quantity = item.quantity + membershipItem.quantity;
                    productArr.membership.splice(i, 1);
                    break;
                  }
                }
                productArr.membership.push({
                  product: item?.product,
                  quantity: item.quantity,
                });
                membershipOrderItemIds.push(item.id);
              }
            }
          }
        }
        const membershipDataId = await this.processCreateOrder(
          queryRunner,
          dto,
          productArr.membership,
          OrderType.MEMBERSHIP,
          idOfMembershipOrder,
          membershipOrderItemIds,
        );
        orderIdArr.push(membershipDataId);
      }
      // Case: others
      if (productArr.others.length > 0) {
        let idOfOthersOrder = null;
        const othersOrderItemIds = [];
        if (currentOrder.length > 0) {
          const othersOrder = currentOrder.find(
            (order) =>
              order.orderType === OrderType.OTHERS &&
              order.status !== InvoiceStatus.VOID,
          );
          if (othersOrder) {
            idOfOthersOrder = othersOrder.id;
            if (othersOrder?.items && othersOrder?.items?.length > 0) {
              for (const item of othersOrder.items) {
                for (let i = 0; i < productArr.others.length; i++) {
                  const othersItem = productArr.others[i];
                  if (
                    (othersItem?.product?.id === item?.product?.id &&
                      othersItem?.product?.type === ProductType.PRODUCT) ||
                    (othersItem?.product?.id === item?.product?.id &&
                      othersItem?.product?.type === ProductType.COUPON &&
                      !othersItem?.product?.couponCode)
                  ) {
                    item.quantity = item.quantity + othersItem.quantity;
                    productArr.others.splice(i, 1);
                    break;
                  } else if (othersItem?.product?.couponCode) {
                    await queryRunner.manager.update(
                      CouponItem,
                      {
                        code: othersItem?.product?.couponCode,
                      },
                      {
                        isUsed: true,
                      },
                    );
                  }
                }

                productArr.others.push({
                  startTime: item?.startTime,
                  endTime: item?.endTime,
                  product: item?.product,
                  quantity: item.quantity,
                  employees: item?.employees,
                  couponCode: item?.couponCode,
                  duration: item?.duration,
                  status: item?.status,
                });
                othersOrderItemIds.push(item.id);
              }
            }
          }
        }
        const othersDataId = await this.processCreateOrder(
          queryRunner,
          dto,
          productArr.others,
          OrderType.OTHERS,
          idOfOthersOrder,
          othersOrderItemIds,
        );
        orderIdArr.push(othersDataId);
      }
      if (orderIdArr.length > 0) {
        for (const orderId of orderIdArr) {
          await Promise.all([
            (order = await queryRunner.manager.findOne(Order, {
              where: { id: orderId },
              relations: [
                'items',
                'items.product',
                'items.product.category',
                'items.employees',
              ],
            })),
            await queryRunner.manager.save(Order, {
              ...order,
              couponName,
              payload: order,
            }),
          ]);
        }
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }

    return await this.repo.find({
      where: { id: In(orderIdArr) },
      relations: ['items', 'items.product', 'items.product.category'],
    });
  }

  async updateOneOrder(req: Request, dto: any, id: string) {
    const findOrder = await this.repo.findOne({
      where: { id },
      relations: [
        'appointment',
        'branch',
        'invoice',
        'invoice.invoiceCoupon',
        'invoice.customer',
        'items',
        'items.product',
        'items.product.category',
      ],
    });

    if (!findOrder) {
      throw new BadRequestException('Order not found');
    }

    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      if (!dto.items) {
        // Save current orders to oldOrder field in invoice before updating
        if (findOrder.invoice && dto.isDraft === false) {
          const currentInvoiceWithOrders = await queryRunner.manager.findOne(
            Invoice,
            {
              where: { id: findOrder.invoice.id },
              relations: ['orders', 'orders.items', 'orders.items.product'],
            },
          );

          if (currentInvoiceWithOrders && currentInvoiceWithOrders.orders) {
            await queryRunner.manager.update(
              Invoice,
              { id: findOrder.invoice.id },
              { oldOrder: currentInvoiceWithOrders.orders },
            );
          }
        }

        // Handle update dto other
        await queryRunner.manager.getRepository(Order).update(
          { id },
          {
            isPrinted: dto?.isPrinted ? dto?.isPrinted : undefined,
            isDraft: dto?.isDraft !== undefined ? dto.isDraft : undefined,
            totalBeforeTax:
              dto?.totalBeforeTax !== undefined
                ? dto.totalBeforeTax
                : undefined,
          },
        );
      } else {
        // Save current orders to oldOrder field in invoice before updating
        if (findOrder.invoice) {
          const currentInvoiceWithOrders = await queryRunner.manager.findOne(
            Invoice,
            {
              where: { id: findOrder.invoice.id },
              relations: ['orders', 'orders.items', 'orders.items.product'],
            },
          );

          if (currentInvoiceWithOrders && currentInvoiceWithOrders.orders) {
            await queryRunner.manager.update(
              Invoice,
              { id: findOrder.invoice.id },
              { oldOrder: currentInvoiceWithOrders.orders },
            );
          }
        }

        //Create audit trail log
        await this.auditTrailService.createRecordLog(queryRunner, {
          createdById: req?.['user']?.['id'],
          branchId: findOrder.branch.id,
          action: AuditTrailOperation.EDIT,
          payload: findOrder.items,
          startTime: req?.['headers']?.['starttime'],
          newPayload: dto.items,
          invoiceId: null,
          orderId: findOrder.id,
        });
        if (dto.items.length) {
          const dtItems = await this.mergedItems(dto.items);
          // Handle update items of order
          if (dtItems.length) {
            const productArr = {
              membership: [],
              food_beverage: [],
              others: [],
            };
            for (const item of dtItems) {
              switch (item.type) {
                case ProductType.MEMBERSHIP:
                  productArr['membership'].push(item);
                  break;
                case ProductType.BEVERAGE:
                case ProductType.FOOD:
                  productArr['food_beverage'].push(item);
                  break;
                default:
                  productArr['others'].push(item);
                  break;
              }
            }

            //handle kitchen order
            if (productArr.food_beverage.length > 0) {
              const foodItems = productArr.food_beverage.filter(
                (product) => product.type === ProductType.FOOD,
              );
              // if (foodItems.length > 0) {
              await this.handleKitchenOrder(
                queryRunner,
                ProductType.FOOD,
                foodItems,
                findOrder,
              );
              // }

              const beverageItems = productArr.food_beverage.filter(
                (product) => product.type === ProductType.BEVERAGE,
              );
              // if (beverageItems.length > 0) {
              await this.handleKitchenOrder(
                queryRunner,
                ProductType.BEVERAGE,
                beverageItems,
                findOrder,
              );
              // }
            }

            if (!findOrder.invoice) {
              // Case: Unpaid
              const currentOrders = await this.repo.find({
                where: {
                  appointment: { id: findOrder.appointment.id },
                  invoice: IsNull(),
                },
                relations: ['items', 'items.product', 'items.product.category'],
              });
              if (currentOrders.length) {
                if (productArr.membership.length) {
                  const membershipOrder = currentOrders.find(
                    (order) => order.orderType === OrderType.MEMBERSHIP,
                  );
                  await this.processUnPaidPrice(
                    queryRunner,
                    productArr.membership,
                    membershipOrder,
                    OrderType.MEMBERSHIP,
                    findOrder.appointment,
                    findOrder.branch,
                  );
                }
                if (productArr.others.length) {
                  const otherOrder = currentOrders.find(
                    (order) => order.orderType === OrderType.OTHERS,
                  );
                  await this.processUnPaidPrice(
                    queryRunner,
                    productArr.others,
                    otherOrder,
                    OrderType.OTHERS,
                    findOrder.appointment,
                    findOrder.branch,
                  );
                }
                if (productArr.food_beverage.length) {
                  const otherOrder = currentOrders.find(
                    (order) => order.orderType === OrderType.FOOD_BEVERAGE,
                  );
                  await this.processUnPaidPrice(
                    queryRunner,
                    productArr.food_beverage,
                    otherOrder,
                    OrderType.FOOD_BEVERAGE,
                    findOrder.appointment,
                    findOrder.branch,
                  );
                }
              }
            } else {
              // Case: Paid
              // if (productArr.membership.length) {
              // await this.processPaidPrice(
              //   queryRunner,
              //   productArr.membership,
              //   findOrder,
              // );
              // }
              if (productArr.others.length) {
                await this.processPaidPrice(
                  queryRunner,
                  productArr.others,
                  findOrder,
                );
              }
              if (productArr.food_beverage.length) {
                await this.processPaidPrice(
                  queryRunner,
                  productArr.food_beverage,
                  findOrder,
                );
              }
            }
          }
        } else {
          if (findOrder.status === InvoiceStatus.PAID && findOrder.invoice) {
            for (const discountItem of findOrder.invoice.invoiceCoupon) {
              if (!!discountItem.couponCode) {
                throw new BadRequestException('The order cannot be updated');
              }
            }
            // if (findOrder.invoice.discount) {
            //   throw new BadRequestException('The order cannot be updated');
            // }
            // Handle update when items are empty
            await this.processOrderEmpty(queryRunner, findOrder, dto);
          } else {
            const fbOrders = await queryRunner.manager.find(OrderDetail, {
              where: {
                order: {
                  id: findOrder.id,
                },
                fbOrder: {
                  id: Not(IsNull()),
                },
              },
            });
            if (findOrder.orderType === OrderType.FOOD_BEVERAGE) {
              await queryRunner.manager.update(
                FbOrder,
                {
                  appointment: { id: findOrder.appointment.id },
                  orderType: OrderType.FOOD_BEVERAGE,
                },
                { status: FbOrderStatus.CANCEL },
              );
            }

            if (
              findOrder.orderType === OrderType.OTHERS &&
              findOrder.status === InvoiceStatus.UNPAID
            ) {
              // When voiding order, update status and isEdited, and apply other DTO fields
              const updateData: any = {
                isEdited: true,
                status: InvoiceStatus.VOID,
              };

              // Apply safe fields from DTO
              if (dto.totalBeforeTax !== undefined)
                updateData.totalBeforeTax = dto.totalBeforeTax;
              if (dto.isDraft !== undefined) updateData.isDraft = dto.isDraft;
              if (dto.note !== undefined) updateData.note = dto.note;
              if (dto.discount !== undefined)
                updateData.discount = dto.discount;
              if (dto.tax !== undefined) updateData.tax = dto.tax;
              if (dto.total !== undefined) updateData.total = dto.total;
              if (dto.subTotal !== undefined)
                updateData.subTotal = dto.subTotal;
              if (dto.discountMoney !== undefined)
                updateData.discountMoney = dto.discountMoney;
              if (dto.couponCode !== undefined)
                updateData.couponCode = dto.couponCode;
              if (dto.couponName !== undefined)
                updateData.couponName = dto.couponName;
              if (dto.isPrinted !== undefined)
                updateData.isPrinted = dto.isPrinted;

              await queryRunner.manager.update(
                Order,
                { id: findOrder.id },
                updateData,
              );
              // update status order detail
              await queryRunner.manager.update(
                OrderDetail,
                {
                  order: {
                    id: findOrder.id,
                  },
                },
                {
                  status: AppointmentStatus.CANCEL,
                },
              );
            } else {
              await queryRunner.manager.delete(OrderDetail, {
                order: findOrder,
              });
              await queryRunner.manager.delete(Order, {
                id: findOrder.id,
              });
            }
          }
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      console.log('error: ', error);
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(error.message);
    } finally {
      await queryRunner.release();
    }

    const getOrder = await this.repo.findOne({
      where: { id },
      relations: [
        'appointment',
        'branch',
        'invoice',
        'items',
        'items.product',
        'items.product.category',
      ],
    });

    return getOrder ? getOrder : {};
  }

  private async handleKitchenOrder(
    queryRunner,
    type: ProductType.BEVERAGE | ProductType.FOOD,
    productArr: any[],
    order: Order,
  ) {
    //get all kitchen order of appointment
    const kitchenOrders = await this.repo.manager.find(FbOrder, {
      where: {
        appointment: {
          id: order.appointment.id,
        },
        productType: type,
      },
      relations: ['items', 'items.product'],
    });

    //I.create new kitchen order
    //I.1 get list all item is completed
    const completedProductsIds = {};
    kitchenOrders.forEach((fb_order) => {
      if (fb_order.isComplete) {
        fb_order.items.forEach((item) => {
          if (!completedProductsIds[item.product.id]) {
            completedProductsIds[item.product.id] = 0;
          }
          completedProductsIds[item.product.id] += item.quantity;
        });
      }
    });

    //I.2 compare with list order f_b to get new item need to send the kitchen
    const newItems = [];
    productArr.forEach((item) => {
      if (!completedProductsIds[item.id]) {
        newItems.push({
          product: item,
          note: item.note,
          quantity: item.quantity,
        });
      } else if (completedProductsIds[item.id] < item.quantity) {
        newItems.push({
          product: item,
          note: item.note,
          quantity: item.quantity - completedProductsIds[item.id],
        });
      }
    });

    //I.3 create kitchen order
    if (
      newItems.length > 0
      // productArr.food_beverage.length < findOrder.items.length
    ) {
      let sumSubTotal = 0;

      for (const item of newItems) {
        const quantity = item?.quantity ?? 1;
        const price = parseFloat(item?.product?.price.toFixed(2)) * quantity;
        sumSubTotal += price;
      }

      //cancel all kitchen order have not completed
      await queryRunner.manager.update(
        FbOrder,
        {
          appointment: {
            id: order.appointment.id,
          },
          isComplete: false,
          status: Not(FbOrderStatus.COMPLETED),
          productType: type,
        },
        {
          status: FbOrderStatus.CANCEL,
        },
      );

      //create new kitchen order

      const fbOrder = await queryRunner.manager.save(FbOrder, {
        items: newItems,
        orderType: OrderType.FOOD_BEVERAGE,
        appointment: { id: order.appointment.id },
        branch: { id: order.branch.id },
        total: parseFloat(sumSubTotal.toFixed(2)),
        subTotal: parseFloat(sumSubTotal.toFixed(2)),
        totalBeforeTax: parseFloat(sumSubTotal.toFixed(2)),
        status: FbOrderStatus.NEW,
        productType: type,
      });

      for (const item of newItems) {
        await queryRunner.manager.save(OrderDetail, {
          ...item,
          price: item.quantity * parseFloat(item.product.price.toFixed(2)),
          fbOrder: fbOrder,
        });
      }
    } else {
      //cancel all kitchen order have not completed
      await queryRunner.manager.update(
        FbOrder,
        {
          appointment: {
            id: order.appointment.id,
          },
          isComplete: false,
          status: Not(FbOrderStatus.COMPLETED),
          productType: type,
        },
        {
          status: FbOrderStatus.CANCEL,
        },
      );
    }

    //end of update kitchen
  }

  async orderFromMobile(req: Request, dto: CreateOrderFromMobileDto) {
    const rfid = await this.rfidRepo.findOne({
      where: { token: dto.token },
      relations: ['appointment'],
    });

    if (!rfid) {
      throw new NotFoundException('RFID not found');
    }

    if (!rfid.appointment) {
      throw new BadRequestException('RFID is already checked out');
    }

    dto.appointment = rfid.appointment;
    return await this.createOneOrder(req, dto);
  }

  // Handle save data of order. (Using queryRunner)
  async processCreateOrder(
    queryRunner,
    dto: any,
    productArray: any,
    type: string,
    idOfOrder: null,
    orderItemIds: any,
  ) {
    let order;
    if (idOfOrder) {
      if (orderItemIds.length > 0) {
        await queryRunner.manager.delete(OrderDetail, {
          id: In(orderItemIds),
        });
      }
      order = await queryRunner.manager.save(Order, {
        id: idOfOrder,
        ...dto,
      });
    } else {
      order = await queryRunner.manager.save(Order, {
        ...dto,
        orderType: type,
      });
    }
    dto.items = productArray.map((item) => ({
      ...item,
      duration: item.duration || item.product?.duration || null,
    }));
    await this.processTaxForOrder(queryRunner, dto, order);

    // Update isUsed=true for CouponItem if couponCode is present in any item
    await Promise.all(
      productArray
        .filter((item) => item.couponCode)
        .map(async (item) => {
          await queryRunner.manager.update(
            'coupon_item',
            { code: item.couponCode },
            { isUsed: true },
          );
        }),
    );
    return order.id;
  }

  // Handle tax of order. (Using queryRunner)
  async processTaxForOrder(queryRunner, dto: any, order: any) {
    let totalBeforeTax = 0;
    await Promise.all(
      dto.items.map(async (item) => {
        if (item?.product?.id) {
          const productInfo = await queryRunner.manager.findOne(Product, {
            where: { id: item?.product?.id },
          });
          if (productInfo && !item.couponCode) {
            totalBeforeTax +=
              parseFloat(productInfo?.price.toFixed(2)) * (item?.quantity ?? 1);
          }
        }
        await queryRunner.manager.save(OrderDetail, {
          ...item,
          price: !item.couponCode
            ? item.quantity * parseFloat(item.product.price.toFixed(2))
            : 0,
          order: order,
          status: item?.status ? item?.status : AppointmentStatus.BOOKING,
          duration: item.duration || item.product?.duration || null,
        });
      }),
    );

    const subTotal = parseFloat(totalBeforeTax.toFixed(2));
    const total = parseFloat(totalBeforeTax.toFixed(2));
    await queryRunner.manager.save(Order, {
      id: order.id,
      total:
        parseFloat(total.toFixed(2)) < 0 ? 0 : parseFloat(total.toFixed(2)),
      subTotal,
      totalBeforeTax:
        parseFloat(totalBeforeTax.toFixed(2)) < 0
          ? 0
          : parseFloat(totalBeforeTax.toFixed(2)),
    });
  }

  // Handle tax of F&B order. (Using queryRunner)
  async processTaxForFbOrder(queryRunner, dto: any, order: any) {
    let totalBeforeTax = 0;
    await Promise.all(
      dto.items.map(async (item) => {
        if (item?.product?.id) {
          const productInfo = await queryRunner.manager.findOne(Product, {
            where: { id: item?.product?.id },
          });
          if (productInfo) {
            totalBeforeTax +=
              parseFloat(productInfo?.price.toFixed(2)) * (item?.quantity ?? 1);
          }
        }
        await queryRunner.manager.save(OrderDetail, {
          ...item,
          price: item.quantity * parseFloat(item.product.price.toFixed(2)),
          fbOrder: order,
        });
      }),
    );

    const subTotal = parseFloat(totalBeforeTax.toFixed(2));
    const total = parseFloat(totalBeforeTax.toFixed(2));
    await queryRunner.manager.save(FbOrder, {
      id: order.id,
      total:
        parseFloat(total.toFixed(2)) < 0 ? 0 : parseFloat(total.toFixed(2)),
      subTotal,
      totalBeforeTax:
        parseFloat(totalBeforeTax.toFixed(2)) < 0
          ? 0
          : parseFloat(totalBeforeTax.toFixed(2)),
    });
  }

  async transferCustomerOrder(req: Request, dto: TransferFbOrder) {
    const order = await this.repo.findOne({
      where: { id: dto.orderId },
      relations: ['appointment.customer', 'invoice'],
    });
    if (!order) {
      throw new NotFoundException('The order not found');
    }
    if (order.invoice) {
      throw new NotFoundException('The order is paid');
    }
    if (!order.appointment.customer) {
      throw new BadRequestException('The order does not have customer');
    }
    const transferCustomerId = order.appointment.customer.id;

    const appointment = await this.appointmentRepo.findOne({
      where: {
        customer: { id: dto.customerId },
        checkIn: Not(IsNull()),
        checkOut: IsNull(),
      },
      relations: ['branch', 'customer'],
    });
    if (!appointment) {
      throw new NotFoundException('Customer not checkin');
    }
    let newOrder = null;
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      newOrder = await queryRunner.manager.update(
        Order,
        { id: dto.orderId },
        {
          appointment: appointment,
          transferBy: { id: transferCustomerId },
          orderType: OrderType.TRANSFER,
        },
      );

      const user = await queryRunner.manager.findOne(User, {
        where: { id: req?.['user']?.['id'] },
      });

      //Create audit trail log
      await this.auditTrailService.createRecordLog(queryRunner, {
        createdById: req?.['user']?.['id'],
        branchId: appointment.branch?.id,
        action: AuditTrailOperation.TRANSFER,
        payload: [],
        startTime: req?.['headers']?.['starttime'],
        newPayload: [],
        invoiceId: null,
        orderId: dto.orderId,
        oldCustomerName:
          order?.appointment?.customer?.firstName +
          ' ' +
          order?.appointment?.customer?.lastName,
        newCustomerName:
          appointment?.customer?.firstName +
          ' ' +
          appointment?.customer?.lastName,
        createdByName:
          user?.username || user?.displayName || user?.fullname || '',
      });
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(error);
    } finally {
      // you need to release query runner which is manually created:
      await queryRunner.release();
    }
    return newOrder;
  }

  // Handle list item duplicate id
  async mergedItems(items: any) {
    const mergedItems = items.reduce((acc, curr) => {
      const key = `${curr.product.id}-${curr?.couponCode ?? ''}`;
      if (acc[key]) {
        acc[key].quantity += curr.quantity;
      } else {
        acc[key] = { ...curr };
      }
      return acc;
    }, {});

    const dtProduct = [];
    const resultItems = Object.values(mergedItems) as any;
    if (resultItems.length) {
      for (const item of resultItems) {
        const findProduct = await this.repo.manager.findOne(Product, {
          where: { id: item?.product?.id },
          relations: ['duration', 'category'],
        });
        if (findProduct) {
          dtProduct.push({
            ...findProduct,
            quantity: item.quantity,
            startTime: item?.startTime ? item?.startTime : undefined,
            endTime: item?.endTime ? item?.endTime : undefined,
            note: item?.note ? item?.note : undefined,
            couponCode: item?.couponCode ? item?.couponCode : undefined,
          });
        }
      }
    }
    return dtProduct;
  }

  // Handle update order detail with case un-paid. (Using queryRunner)
  async processUnPaidPrice(
    queryRunner,
    items: any,
    order: any,
    orderType: any,
    appointment: any,
    branch: any,
  ) {
    if (order) {
      // TODO:Update coupon
      // Handle update old order
      await queryRunner.manager.delete(OrderDetail, {
        order,
      });
      let sumSubTotal = 0;
      for (const item of items) {
        const quantity = item?.quantity ?? 1;
        const price = !item.couponCode
          ? parseFloat(item?.price.toFixed(2)) * quantity
          : 0;
        sumSubTotal += price;
        const omitDataSaving = await this.omitDataOrderDetail(item);
        await this.updateOrderDetail(
          queryRunner,
          order,
          price,
          quantity,
          omitDataSaving.newItem,
          omitDataSaving.note,
          omitDataSaving.startTime,
          omitDataSaving.endTime,
          omitDataSaving.couponCode,
        );
      }
      await queryRunner.manager.save(Order, {
        id: order.id,
        total: parseFloat(sumSubTotal.toFixed(2)),
        subTotal: parseFloat(sumSubTotal.toFixed(2)),
        totalBeforeTax: parseFloat(sumSubTotal.toFixed(2)), // Keep entity old
      });
      await this.updatePayload(queryRunner, order);
    }
    return;
  }

  // Handle update order detail with case paid. (Using queryRunner)
  async processPaidPrice(queryRunner, items: any, order: any) {
    // Handle update old order
    await queryRunner.manager.delete(OrderDetail, {
      order,
    });
    let sumSubTotal = 0;
    for (const item of items) {
      const quantity = item?.quantity ?? 1;
      const price = !item.couponCode
        ? parseFloat(item?.price.toFixed(2)) * quantity
        : 0;
      sumSubTotal += price;
      const omitDataSaving = await this.omitDataOrderDetail(item);
      await this.updateOrderDetail(
        queryRunner,
        order,
        price,
        quantity,
        omitDataSaving.newItem,
        omitDataSaving.note,
        omitDataSaving.startTime,
        omitDataSaving.endTime,
        omitDataSaving.couponCode,
      );
    }
    await queryRunner.manager.save(Order, {
      id: order.id,
      total: parseFloat(sumSubTotal.toFixed(2)),
      subTotal: parseFloat(sumSubTotal.toFixed(2)),
      totalBeforeTax: parseFloat(sumSubTotal.toFixed(2)), // Keep entity old
      isEdited: true,
    });
    await this.updatePayload(queryRunner, order);
  }

  // Handle update order when items empty. (Using queryRunner)
  async processOrderEmpty(queryRunner, order: Order, dto?: any) {
    await queryRunner.manager.delete(OrderDetail, {
      order,
    });
    // When voiding order due to empty items, update status and isEdited, and apply other DTO fields
    const updateData: any = {
      isEdited: true,
      status: InvoiceStatus.VOID,
    };

    // Apply safe fields from DTO if provided
    if (dto) {
      if (dto.totalBeforeTax !== undefined) {
        updateData.totalBeforeTax = dto.totalBeforeTax;
        updateData.subTotal = dto.totalBeforeTax;
        updateData.total = dto.totalBeforeTax;
      }
      if (dto.isDraft !== undefined) updateData.isDraft = dto.isDraft;
      if (dto.note !== undefined) updateData.note = dto.note;
      if (dto.discount !== undefined) updateData.discount = dto.discount;
      if (dto.tax !== undefined) updateData.tax = dto.tax;
      // if (dto.total !== undefined) updateData.total = dto.total;
      // if (dto.subTotal !== undefined) updateData.subTotal = dto.subTotal;
      if (dto.discountMoney !== undefined)
        updateData.discountMoney = dto.discountMoney;
      if (dto.couponCode !== undefined) updateData.couponCode = dto.couponCode;
      if (dto.couponName !== undefined) updateData.couponName = dto.couponName;
      if (dto.isPrinted !== undefined) updateData.isPrinted = dto.isPrinted;
    }

    await queryRunner.manager.update(Order, { id: order.id }, updateData);
    await this.updatePayload(queryRunner, order);
  }

  // Handle update payload when order detail change information. (Using queryRunner)
  async updatePayload(queryRunner, order: any) {
    const orderPayload = await queryRunner.manager.findOne(Order, {
      where: { id: order.id },
      relations: ['items', 'items.product', 'items.product.category'],
    });
    await queryRunner.manager.save(Order, {
      id: order.id,
      payload: orderPayload,
    });
  }

  async omitDataOrderDetail(item: any) {
    let note;
    let newItem = _.omit(item, 'quantity');
    if (item.note) {
      note = item.note;
      newItem = _.omit(item, 'note');
    }
    let startTime;
    let endTime;
    if (item.startTime) {
      startTime = item.startTime;
      newItem = _.omit(item, 'startTime');
    }
    if (item.endTime) {
      endTime = item.endTime;
      newItem = _.omit(item, 'endTime');
    }
    let couponCode;
    if (item.couponCode) {
      couponCode = item.couponCode;
      newItem = _.omit(item, 'couponCode');
    }
    return {
      newItem,
      note,
      startTime,
      endTime,
      couponCode,
    };
  }

  // Handle update order detail when change information. (Using queryRunner)
  async updateOrderDetail(
    queryRunner,
    order: any,
    price: number,
    quantity: number,
    item: any,
    note: any,
    startTime?: any,
    endTime?: any,
    couponCode?: any,
  ) {
    await queryRunner.manager.save(OrderDetail, {
      order,
      product: item,
      price: parseFloat(price.toFixed(2)),
      quantity,
      duration: {
        id: item.duration ? item.duration.id : undefined,
      },
      note,
      startTime,
      endTime,
      couponCode,
    });
  }

  async countByStatus(crudReq: CrudRequest) {
    const data: any = {
      isDraft: false,
    };

    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'created' && f.operator === '$between') {
          data.startTime = f.value[0];
          data.endTime = f.value[1];
        }

        if (f.field === 'isDraft' && f.operator === '$eq') {
          data.isDraft = f.value;
        }
      }
    }

    const masterQuery = this.repo
      .createQueryBuilder('Order')
      .select(['"Order".status AS status', 'COUNT(*) AS total'])
      .leftJoin('Order.appointment', 'appointment')
      .leftJoin('Order.branch', 'branch')
      .where('Order.isDraft = :isDraft', { isDraft: data.isDraft });

    // Only add time filter if startTime and endTime exist
    if (data.startTime && data.endTime) {
      masterQuery.andWhere('(Order.created BETWEEN :startTime and :endTime)', {
        startTime: data.startTime,
        endTime: data.endTime,
      });
    }

    masterQuery.andWhere('appointment.rfid IS NOT NULL');
    masterQuery.groupBy('"Order".status');

    this.setSearchCondition(masterQuery, crudReq.parsed.search);

    return await masterQuery.getRawMany();
  }
}
