import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, IsDateString } from 'class-validator';

export class CustomerPrepaidDetailRowDTO {
  @ApiProperty({ description: 'Row number in CSV' })
  @IsString()
  index: string;

  @ApiProperty({ description: 'Customer code' })
  @IsString()
  code: string;

  @ApiProperty({ description: 'Customer full name' })
  @IsString()
  customer: string;

  @ApiPropertyOptional({ description: 'Sharing information' })
  @IsOptional()
  @IsString()
  sharing?: string;

  @ApiProperty({ description: 'Purchase date' })
  @IsString()
  purchaseDate: string;

  @ApiProperty({ description: 'Reference number (TK######: IN######)' })
  @IsString()
  referenceNo: string;

  @ApiProperty({ description: 'Item code (e.g., S01094, C01058)' })
  @IsString()
  itemCode: string;

  @ApiProperty({ description: 'Item description' })
  @IsString()
  item: string;

  @ApiPropertyOptional({ description: 'Item remark' })
  @IsOptional()
  @IsString()
  itemRemark?: string;

  @ApiProperty({ description: 'Consumed amount' })
  @IsString()
  consumed: string;

  @ApiProperty({ description: 'Balance amount' })
  @IsString()
  balance: string;

  @ApiProperty({ description: 'Quantity' })
  @IsString()
  qty: string;

  @ApiProperty({ description: 'Value in SGD' })
  @IsString()
  value: string;

  @ApiPropertyOptional({ description: 'Last consumed date' })
  @IsOptional()
  @IsString()
  lastConsumed?: string;

  @ApiPropertyOptional({ description: 'Expiry date' })
  @IsOptional()
  @IsString()
  expiryDate?: string;

  @ApiProperty({ description: 'Price in SGD' })
  @IsString()
  price: string;

  @ApiProperty({ description: 'Total in SGD' })
  @IsString()
  total: string;

  @ApiProperty({ description: 'Purchased at location' })
  @IsString()
  purchasedAt: string;

  @ApiPropertyOptional({ description: 'Prepaid remark' })
  @IsOptional()
  @IsString()
  prepaidRemark?: string;
}

export class ParsedCustomerPrepaidDetailDTO {
  @ApiProperty({ description: 'Customer code as string (legacy code)' })
  customerCode: string;

  @ApiProperty({ description: 'Customer first name' })
  firstName: string;

  @ApiProperty({ description: 'Customer last name' })
  lastName: string;

  @ApiProperty({ description: 'Purchase date as Date object' })
  purchaseDate: Date;

  @ApiProperty({ description: 'Invoice code extracted from reference number' })
  invoiceCode: string;

  @ApiProperty({ description: 'Ticket code extracted from reference number' })
  ticketCode: string;

  @ApiProperty({ description: 'Item/Product code' })
  itemCode: string;

  @ApiProperty({ description: 'Item description' })
  itemDescription: string;

  @ApiProperty({ description: 'Consumed amount as number' })
  consumed: number;

  @ApiProperty({ description: 'Balance amount as number' })
  balance: number;

  @ApiProperty({ description: 'Quantity as number' })
  quantity: number;

  @ApiProperty({ description: 'Value as number' })
  value: number;

  @ApiPropertyOptional({ description: 'Last consumed date' })
  lastConsumed?: Date;

  @ApiPropertyOptional({ description: 'Expiry date' })
  expiryDate?: Date;

  @ApiProperty({ description: 'Price as number' })
  price: number;

  @ApiProperty({ description: 'Total as number' })
  total: number;

  @ApiProperty({ description: 'Branch/Location code' })
  branchCode: string;

  @ApiProperty({ description: 'Original CSV row data' })
  originalRow: CustomerPrepaidDetailRowDTO;
}

export class ImportRowErrorDTO {
  @ApiProperty({ description: 'Row number where error occurred' })
  row: number;

  @ApiProperty({ description: 'Original row data' })
  data: CustomerPrepaidDetailRowDTO;

  @ApiProperty({ description: 'List of error messages' })
  errors: string[];
}

export class ImportSummaryDTO {
  @ApiProperty({ description: 'Number of customers processed' })
  customersProcessed: number;

  @ApiProperty({ description: 'Number of credits created' })
  creditsCreated: number;

  @ApiProperty({ description: 'Number of invoices created' })
  invoicesCreated: number;

  @ApiProperty({ description: 'Number of credit histories created' })
  creditHistoriesCreated: number;

  @ApiProperty({ description: 'Number of orders created' })
  ordersCreated: number;
}

export class CustomerPrepaidDetailImportResultDTO {
  @ApiProperty({ description: 'Total number of rows processed' })
  totalRows: number;

  @ApiProperty({ description: 'Number of successful imports' })
  successCount: number;

  @ApiProperty({ description: 'Number of failed imports' })
  errorCount: number;

  @ApiProperty({ description: 'List of errors for failed rows' })
  errors: ImportRowErrorDTO[];

  @ApiProperty({ description: 'Summary of created records' })
  summary: ImportSummaryDTO;

  @ApiProperty({ description: 'Import completion timestamp' })
  completedAt: Date;

  @ApiProperty({ description: 'Total processing time in milliseconds' })
  processingTimeMs: number;
}
