import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { Customer } from '../../../customer/customer.entity';
import { Credit } from '../../../credit/credit.entity';
import { CreditHistory } from '../../../credit-history/credit-history.entity';
import { Product } from '../../../product/product.entity';
import { Invoice } from '../../../invoice/invoice.entity';
import { Order } from '../../../order/order.entity';
import { OrderDetail } from '../../../order-detail/order-detail.entity';
import { Branch } from '../../../branch/branch.entity';
import { CreditSetting } from '../../../settings/credit/credit-setting.entity';
import { Setting } from '../../../setting/setting.entity';
import { Category } from '../../../category/category.entity';
import * as csv from 'csv-parser';
import { Readable } from 'stream';
import * as moment from 'moment-timezone';
import {
  CustomerPrepaidDetailRowDTO,
  ParsedCustomerPrepaidDetailDTO,
  CustomerPrepaidDetailImportResultDTO,
  ImportRowErrorDTO,
  ImportSummaryDTO,
} from '../dto/customer-prepaid-detail-import.dto';
import {
  CreditStatus,
  InvoiceStatus,
  OrderType,
  ProductType,
} from 'src/core/enums/entity';

@Injectable()
export class CustomerPrepaidDetailImportService {
  private readonly logger = new Logger(CustomerPrepaidDetailImportService.name);

  constructor(
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Credit)
    private creditRepo: Repository<Credit>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Branch)
    private branchRepo: Repository<Branch>,
    @InjectRepository(CreditSetting)
    private creditSettingRepo: Repository<CreditSetting>,
    private dataSource: DataSource,
  ) {}

  /**
   * Import customer prepaid detail data from CSV buffer
   */
  async importFromCSV(
    buffer: Buffer,
  ): Promise<CustomerPrepaidDetailImportResultDTO> {
    const startTime = Date.now();
    this.logger.log('Starting customer prepaid detail import process');

    try {
      // Parse CSV
      const csvRows = await this.parseCSV(buffer);
      this.logger.log(`Parsed ${csvRows.length} rows from CSV`);

      // Process the data
      const result = await this.processImportData(csvRows);

      result.completedAt = new Date();
      result.processingTimeMs = Date.now() - startTime;

      this.logger.log(`Import completed in ${result.processingTimeMs}ms`);
      return result;
    } catch (error) {
      this.logger.error('Import process failed', error);
      return {
        totalRows: 0,
        successCount: 0,
        errorCount: 1,
        errors: [
          {
            row: 0,
            data: {} as CustomerPrepaidDetailRowDTO,
            errors: [`Import failed: ${error.message}`],
          },
        ],
        summary: {
          customersProcessed: 0,
          creditsCreated: 0,
          invoicesCreated: 0,
          creditHistoriesCreated: 0,
          ordersCreated: 0,
        },
        completedAt: new Date(),
        processingTimeMs: Date.now() - startTime,
      };
    }
  }

  /**
   * Parse CSV buffer into structured data
   */
  private async parseCSV(
    buffer: Buffer,
  ): Promise<CustomerPrepaidDetailRowDTO[]> {
    return new Promise((resolve, reject) => {
      const results: CustomerPrepaidDetailRowDTO[] = [];
      const stream = Readable.from(buffer);

      stream
        .pipe(
          csv([
            'index',
            'code',
            'customer',
            'sharing',
            'purchaseDate',
            'referenceNo',
            'itemCode',
            'item',
            'itemRemark',
            'consumed',
            'balance',
            'qty',
            'value',
            'lastConsumed',
            'expiryDate',
            'price',
            'total',
            'purchasedAt',
            'prepaidRemark',
          ]),
        )
        .on('data', (data) => {
          // Skip header row and grand total row
          // Handle BOM character and various header formats
          const cleanIndex = data.index?.replace(/^\uFEFF/, '').trim(); // Remove BOM
          const cleanCode = data.code?.trim();
          const cleanCustomer = data.customer?.trim();

          if (
            cleanIndex === '#' ||
            cleanIndex === 'index' ||
            cleanCode === 'Code' ||
            cleanCustomer === 'Customer' ||
            cleanCustomer === 'Grand Total' ||
            cleanCustomer?.toLowerCase().includes('customer') ||
            !cleanCode ||
            cleanCode === ''
          ) {
            return;
          }
          results.push(data);
        })
        .on('end', () => {
          this.logger.log(`Successfully parsed ${results.length} data rows`);
          resolve(results);
        })
        .on('error', (error) => {
          this.logger.error('CSV parsing failed', error);
          reject(error);
        });
    });
  }

  /**
   * Parse and validate a single CSV row
   */
  private parseRow(
    row: CustomerPrepaidDetailRowDTO,
    rowIndex: number,
  ): {
    parsed?: ParsedCustomerPrepaidDetailDTO;
    errors: string[];
  } {
    const errors: string[] = [];

    try {
      // Validate required fields
      if (
        !row.code ||
        !row.customer ||
        !row.purchaseDate ||
        !row.referenceNo ||
        !row.itemCode
      ) {
        errors.push(
          'Missing required fields: code, customer, purchaseDate, referenceNo, or itemCode',
        );
        return { errors };
      }

      // Use customer code as string for legacyCode mapping
      const customerCode = row.code.trim();
      if (!customerCode) {
        errors.push(`Customer code cannot be empty`);
      }

      // Parse customer name
      const customerName = row.customer.trim();
      if (!customerName) {
        errors.push('Customer name cannot be empty');
      }
      const nameParts = customerName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Parse purchase date
      let purchaseDate: Date;
      try {
        purchaseDate = moment(row.purchaseDate, 'DD-MM-YYYY').toDate();
        if (!moment(purchaseDate).isValid()) {
          errors.push(`Invalid purchase date: ${row.purchaseDate}`);
        }
      } catch (e) {
        errors.push(`Invalid purchase date format: ${row.purchaseDate}`);
      }

      // Parse reference number to extract codes
      const refMatch = row.referenceNo.match(/TK(\d+):\s*IN(\d+)/);
      if (!refMatch) {
        errors.push(`Invalid reference number format: ${row.referenceNo}`);
      }
      const ticketCode = refMatch ? `TK${refMatch[1]}` : '';
      const invoiceCode = refMatch ? `IN${refMatch[2]}` : '';

      // Parse numeric values
      const consumed = this.parseNumericValue(row.consumed, 'consumed');
      const balance = this.parseNumericValue(row.balance, 'balance');
      const quantity = this.parseNumericValue(row.qty, 'quantity');
      const value = this.parseNumericValue(row.value, 'value');
      const price = this.parseNumericValue(row.price, 'price');
      const total = this.parseNumericValue(row.total, 'total');

      // Parse optional dates
      let lastConsumed: Date | undefined;
      if (row.lastConsumed && row.lastConsumed.trim()) {
        try {
          lastConsumed = moment(row.lastConsumed, 'DD-MM-YYYY').toDate();
        } catch (e) {
          // Optional field, don't add error
        }
      }

      let expiryDate: Date | undefined;
      if (row.expiryDate && row.expiryDate.trim()) {
        try {
          expiryDate = moment(row.expiryDate, 'DD-MM-YYYY').toDate();
        } catch (e) {
          // Optional field, don't add error
        }
      }

      // Extract branch code from purchasedAt
      const branchMatch = row.purchasedAt.match(/\[([^\]]+)\]/);
      const branchCode = branchMatch ? branchMatch[1] : 'HQ';

      if (errors.length > 0) {
        return { errors };
      }

      const parsed: ParsedCustomerPrepaidDetailDTO = {
        customerCode,
        firstName,
        lastName,
        purchaseDate,
        invoiceCode,
        ticketCode,
        itemCode: row.itemCode,
        itemDescription: row.item,
        consumed,
        balance,
        quantity,
        value,
        lastConsumed,
        expiryDate,
        price,
        total,
        branchCode,
        originalRow: row,
      };

      return { parsed, errors: [] };
    } catch (error) {
      errors.push(`Row parsing failed: ${error.message}`);
      return { errors };
    }
  }

  /**
   * Parse numeric value from string, handling various formats
   */
  private parseNumericValue(value: string, fieldName: string): number {
    if (!value || value.trim() === '') return 0;

    // Remove common formatting characters
    const cleaned = value.replace(/[,$\s]/g, '');
    const parsed = parseFloat(cleaned);

    if (isNaN(parsed)) {
      throw new Error(`Invalid ${fieldName} value: ${value}`);
    }

    return parsed;
  }

  /**
   * Process parsed import data
   */
  private async processImportData(
    csvRows: CustomerPrepaidDetailRowDTO[],
  ): Promise<CustomerPrepaidDetailImportResultDTO> {
    const result: CustomerPrepaidDetailImportResultDTO = {
      totalRows: csvRows.length,
      successCount: 0,
      errorCount: 0,
      errors: [],
      summary: {
        customersProcessed: 0,
        creditsCreated: 0,
        invoicesCreated: 0,
        creditHistoriesCreated: 0,
        ordersCreated: 0,
      },
      completedAt: new Date(),
      processingTimeMs: 0,
    };

    // Pre-load reference data
    const customers = await this.customerRepo.find();
    const products = await this.productRepo.find();
    const branches = await this.branchRepo.find();
    const creditSettings = await this.creditSettingRepo.find();

    // Create lookup maps using legacyCode for customer mapping
    const customerMap = new Map();
    customers.forEach((c) => {
      // Map by legacyCode if it exists, otherwise fall back to code
      const key = c.legacyCode || c.code.toString();
      customerMap.set(key, c);
    });
    // For products, we'll create a mapping based on item codes
    // Since products don't have codes, we'll need to create a custom mapping
    const productMap = await this.createProductMapping(products);
    const branchMap = new Map(branches.map((b) => [b.code || b.name, b]));
    const defaultCreditSetting =
      creditSettings.find((cs) => cs.creditType === 'credits') ||
      creditSettings[0];

    if (!defaultCreditSetting) {
      throw new Error('No credit setting found');
    }

    // Process each row
    for (let i = 0; i < csvRows.length; i++) {
      const rowIndex = i + 1;
      const csvRow = csvRows[i];

      try {
        // Parse and validate row
        const { parsed, errors } = this.parseRow(csvRow, rowIndex);

        if (errors.length > 0) {
          result.errors.push({
            row: rowIndex,
            data: csvRow,
            errors,
          });
          result.errorCount++;
          continue;
        }

        // Validate business rules
        const validationErrors = await this.validateBusinessRules(
          parsed!,
          customerMap,
          productMap,
          branchMap,
        );

        if (validationErrors.length > 0) {
          result.errors.push({
            row: rowIndex,
            data: csvRow,
            errors: validationErrors,
          });
          result.errorCount++;
          continue;
        }

        // Import the row
        await this.importSingleRow(
          parsed!,
          customerMap,
          productMap,
          branchMap,
          defaultCreditSetting,
          result.summary,
        );

        result.successCount++;

        if (rowIndex % 50 === 0) {
          this.logger.log(`Processed ${rowIndex} rows`);
        }
      } catch (error) {
        this.logger.error(`Error processing row ${rowIndex}`, error);
        result.errors.push({
          row: rowIndex,
          data: csvRow,
          errors: [`Processing failed: ${error.message}`],
        });
        result.errorCount++;
      }
    }

    return result;
  }

  /**
   * Validate business rules for a parsed row
   */
  private async validateBusinessRules(
    parsed: ParsedCustomerPrepaidDetailDTO,
    customerMap: Map<string, Customer>,
    productMap: Map<string, Product>,
    branchMap: Map<string, Branch>,
  ): Promise<string[]> {
    const errors: string[] = [];

    // Check if customer exists - for now, we'll allow missing customers to be created
    if (!customerMap.has(parsed.customerCode)) {
      this.logger.warn(
        `Customer with code ${parsed.customerCode} not found - will create if needed`,
      );
    }

    // Check if product exists - we'll use fallback if not found
    if (!productMap.has(parsed.itemCode) && !productMap.has('DEFAULT')) {
      this.logger.warn(
        `Product with code ${parsed.itemCode} not found - will use fallback`,
      );
    }

    // Check if branch exists - we'll use fallback if not found
    if (
      !branchMap.has(parsed.branchCode) &&
      !branchMap.has('HQ') &&
      branchMap.size === 0
    ) {
      errors.push(`No branches available in the system`);
    }

    // Validate monetary values
    if (parsed.balance < 0) {
      errors.push(
        `Invalid balance: ${parsed.balance}. Balance cannot be negative`,
      );
    }

    if (parsed.total < 0) {
      errors.push(`Invalid total: ${parsed.total}. Total cannot be negative`);
    }

    if (parsed.price < 0) {
      errors.push(`Invalid price: ${parsed.price}. Price cannot be negative`);
    }

    if (parsed.quantity <= 0) {
      errors.push(
        `Invalid quantity: ${parsed.quantity}. Quantity must be greater than 0`,
      );
    }

    // Validate dates
    if (parsed.expiryDate && parsed.expiryDate < parsed.purchaseDate) {
      errors.push(`Expiry date cannot be before purchase date`);
    }

    // Check for duplicate invoice
    try {
      const existingInvoice = await this.invoiceRepo.findOne({
        where: { code: parsed.invoiceCode },
      });
      if (existingInvoice) {
        errors.push(`Invoice ${parsed.invoiceCode} already exists`);
      }
    } catch (error) {
      this.logger.warn(
        `Could not check for duplicate invoice: ${error.message}`,
      );
      // Continue without this validation to avoid blocking the import
    }

    // Check for duplicate credit with same customer and reference
    // Skip this check for now to avoid complex JSONB queries that might cause issues
    // In production, you might want to implement this check differently
    // const existingCredit = await this.creditHistoryRepo.findOne({
    //   where: {
    //     customer: { code: parsed.customerCode },
    //     detail: { originalReference: parsed.originalRow.referenceNo },
    //   },
    // });
    // if (existingCredit) {
    //   errors.push(
    //     `Credit record with reference ${parsed.originalRow.referenceNo} already exists for customer ${parsed.customerCode}`,
    //   );
    // }

    return errors;
  }

  /**
   * Import a single validated row
   */
  private async importSingleRow(
    parsed: ParsedCustomerPrepaidDetailDTO,
    customerMap: Map<string, Customer>,
    productMap: Map<string, Product>,
    branchMap: Map<string, Branch>,
    defaultCreditSetting: CreditSetting,
    summary: ImportSummaryDTO,
  ): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get or create customer
      let customer = customerMap.get(parsed.customerCode);
      if (!customer) {
        customer = await this.createCustomerFromImport(
          queryRunner,
          parsed,
          customerMap,
        );
      }

      // Get product with fallback
      let product =
        productMap.get(parsed.itemCode) || productMap.get('DEFAULT');
      if (!product) {
        // Create a fallback product if none exists
        product = await this.createFallbackProduct(queryRunner, parsed);
        productMap.set('DEFAULT', product);
      }

      // Get branch with fallback
      let branch = branchMap.get(parsed.branchCode) || branchMap.get('HQ');
      if (!branch && branchMap.size > 0) {
        // Use the first available branch as fallback
        branch = Array.from(branchMap.values())[0];
      } else if (!branch) {
        // Create a default branch if none exists
        branch = await this.createDefaultBranch(queryRunner);
        branchMap.set('HQ', branch);
      }

      // Create invoice
      const invoice = await this.createInvoice(
        queryRunner,
        parsed,
        customer,
        branch,
      );
      summary.invoicesCreated++;

      // Create order and order detail
      const order = await this.createOrder(
        queryRunner,
        parsed,
        product,
        branch,
        invoice,
      );
      summary.ordersCreated++;

      // Create credit
      const credit = await this.createCredit(
        queryRunner,
        parsed,
        customer,
        defaultCreditSetting,
        branch,
      );
      summary.creditsCreated++;

      // Create credit history
      await this.createCreditHistory(
        queryRunner,
        parsed,
        customer,
        product,
        credit,
        invoice,
      );
      summary.creditHistoriesCreated++;

      await queryRunner.commitTransaction();
      summary.customersProcessed++;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Create invoice record
   */
  private async createInvoice(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
    customer: Customer,
    branch: Branch | undefined,
  ): Promise<Invoice> {
    const invoice = queryRunner.manager.create(Invoice, {
      code: parsed.invoiceCode,
      customer,
      branch,
      date: parsed.purchaseDate,
      total: parsed.total,
      paid: parsed.total,
      unPaid: 0,
      subTotal: parsed.price,
      totalBeforeTax: parsed.price,
      tax: parsed.total - parsed.price,
      status: InvoiceStatus.PAID,
    });

    return await queryRunner.manager.save(Invoice, invoice);
  }

  /**
   * Create order and order detail records
   */
  private async createOrder(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
    product: Product,
    branch: Branch | undefined,
    invoice: Invoice,
  ): Promise<Order> {
    // Generate order code
    const orderCode = await this.generateOrderCode(queryRunner);

    const order = queryRunner.manager.create(Order, {
      code: orderCode,
      branch,
      invoice,
      orderType: this.getOrderType(product),
      total: parsed.total,
      subTotal: parsed.price,
      totalBeforeTax: parsed.price,
      tax: parsed.total - parsed.price,
      isDraft: false,
      isPrinted: false,
    });

    const savedOrder = await queryRunner.manager.save(Order, order);

    // Create order detail
    const orderDetail = queryRunner.manager.create(OrderDetail, {
      order: savedOrder,
      product,
      quantity: parsed.quantity,
      price: parsed.price,
    });

    await queryRunner.manager.save(OrderDetail, orderDetail);

    return savedOrder;
  }

  /**
   * Create credit record
   */
  private async createCredit(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
    customer: Customer,
    creditSetting: CreditSetting,
    branch: Branch | undefined,
  ): Promise<Credit> {
    const credit = queryRunner.manager.create(Credit, {
      customer,
      creditSetting,
      branch,
      issueDate: parsed.purchaseDate,
      expiryDate:
        parsed.expiryDate ||
        moment(parsed.purchaseDate).add(1, 'year').toDate(),
      total: parsed.total,
      creditBalance: parsed.balance,
      status: CreditStatus.VALID,
    });

    return await queryRunner.manager.save(Credit, credit);
  }

  /**
   * Create credit history record
   */
  private async createCreditHistory(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
    customer: Customer,
    product: Product,
    credit: Credit,
    invoice: Invoice,
  ): Promise<CreditHistory> {
    const creditHistory = queryRunner.manager.create(CreditHistory, {
      credit,
      customer,
      product,
      invoice,
      paid: parsed.total,
      opening: 0,
      closing: parsed.balance,
      usable: parsed.balance,
      expiryDate: credit.expiryDate,
      isMembershipPkg: this.isMembershipProduct(product),
      detail: {
        id: invoice.id,
        code: invoice.code,
        date: parsed.purchaseDate,
        itemCode: parsed.itemCode,
        itemDescription: parsed.itemDescription,
        originalReference: parsed.originalRow.referenceNo,
      },
    });

    return await queryRunner.manager.save(CreditHistory, creditHistory);
  }

  /**
   * Generate unique order code
   */
  private async generateOrderCode(queryRunner: QueryRunner): Promise<number> {
    const count = await queryRunner.manager.count(Order);
    return count + 1;
  }

  /**
   * Determine order type based on product
   */
  private getOrderType(product: Product): OrderType {
    if (product.type === ProductType.MEMBERSHIP) {
      return OrderType.MEMBERSHIP;
    }
    if (
      product.type === ProductType.FOOD ||
      product.type === ProductType.BEVERAGE
    ) {
      return OrderType.FOOD_BEVERAGE;
    }
    return OrderType.OTHERS;
  }

  /**
   * Check if product is a membership product
   */
  private isMembershipProduct(product: Product): boolean {
    return (
      product.type === ProductType.MEMBERSHIP ||
      product.isMember ||
      product.name?.toLowerCase().includes('membership')
    );
  }

  /**
   * Create a mapping from item codes to products
   * Since products don't have codes, we'll create a mapping based on patterns
   */
  private async createProductMapping(
    products: Product[],
  ): Promise<Map<string, Product>> {
    const productMap = new Map<string, Product>();

    // Create a mapping based on common patterns in the CSV data
    // This is a simplified approach - in a real system, you'd want a proper mapping table
    for (const product of products) {
      const name = product.name?.toLowerCase() || '';

      // Map membership products
      if (name.includes('membership') || name.includes('member')) {
        if (name.includes('platinum') || name.includes('platnium')) {
          productMap.set('C01058', product); // $550 Platnium Membership 2024
        }
        if (name.includes('anniversary')) {
          productMap.set('C01069', product); // $780 Anniversary Membership 2025
        }
      }

      // Map service products
      if (name.includes('onsen') || name.includes('spa')) {
        if (name.includes('1 for 1') && name.includes('m')) {
          productMap.set('S01008', product); // Onsen 1 for 1 (M)
        }
        if (name.includes('1 for 1') && name.includes('f')) {
          productMap.set('S01007', product); // Onsen 1 for 1 (F)
        }
        if (name.includes('aroma') && name.includes('90')) {
          productMap.set('S01094', product); // AROMA 90MINS
        }
      }

      // Map product items
      if (name.includes('mask')) {
        productMap.set('P01008', product); // Disposable Mask x 3
      }
      if (name.includes('yukata')) {
        productMap.set('P01002', product); // Yukata Set
      }
    }

    // If no specific mapping found, create a fallback mapping
    // This allows the import to continue even if products don't exist
    // In a real system, you'd want to handle this more gracefully
    if (productMap.size === 0 && products.length > 0) {
      // Use the first product as a fallback for all unmapped codes
      const fallbackProduct = products[0];
      this.logger.warn(
        'No product mappings found, using fallback product for all items',
      );
      productMap.set('DEFAULT', fallbackProduct);
    }

    return productMap;
  }

  /**
   * Create a customer from import data when customer doesn't exist
   */
  private async createCustomerFromImport(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
    customerMap: Map<string, Customer>,
  ): Promise<Customer> {
    this.logger.log(`Creating customer with code ${parsed.customerCode}`);

    try {
      // Get or create default status
      let activeStatus = await queryRunner.manager.findOne(Setting, {
        where: { name: 'Active', type: 'status' },
      });
      if (!activeStatus) {
        activeStatus = await queryRunner.manager.save(Setting, {
          name: 'Active',
          type: 'status',
        });
      }

      // Generate a numeric code for the customer (required field)
      const numericCode = await this.generateCustomerCode(queryRunner);

      const customer = queryRunner.manager.create(Customer, {
        code: numericCode,
        legacyCode: parsed.customerCode, // Store the original CSV code as legacyCode
        firstName: parsed.firstName,
        lastName: parsed.lastName,
        email: `imported_${parsed.customerCode}@example.com`, // Placeholder email
        phone: '', // Empty phone for now
        status: activeStatus,
      });

      const savedCustomer = await queryRunner.manager.save(Customer, customer);

      // Update the customer map with the new customer using legacyCode as key
      customerMap.set(parsed.customerCode, savedCustomer);

      return savedCustomer;
    } catch (error) {
      this.logger.error(`Failed to create customer: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate a unique numeric customer code
   */
  private async generateCustomerCode(
    queryRunner: QueryRunner,
  ): Promise<number> {
    const lastCustomer = await queryRunner.manager.findOne(Customer, {
      order: { code: 'DESC' },
    });

    return lastCustomer ? lastCustomer.code + 1 : 100000; // Start from 100000 if no customers exist
  }

  /**
   * Create a fallback product when no products exist
   */
  private async createFallbackProduct(
    queryRunner: QueryRunner,
    parsed: ParsedCustomerPrepaidDetailDTO,
  ): Promise<Product> {
    this.logger.log(
      `Creating fallback product for item code ${parsed.itemCode}`,
    );

    try {
      // Get or create default category
      let defaultCategory = await queryRunner.manager.findOne(Category, {
        where: { name: 'Imported' },
      });
      if (!defaultCategory) {
        // Get first available branch for the category
        const branches = await queryRunner.manager.find(Branch, {
          take: 1,
          order: { created: 'ASC' },
        });
        const firstBranch = branches[0];
        if (firstBranch) {
          defaultCategory = await queryRunner.manager.save(Category, {
            name: 'Imported',
            branch: firstBranch,
          });
        }
      }

      // Get or create default duration setting
      let defaultDuration = await queryRunner.manager.findOne(Setting, {
        where: { name: '60 Minutes', type: 'duration' },
      });
      if (!defaultDuration) {
        defaultDuration = await queryRunner.manager.save(Setting, {
          name: '60 Minutes',
          type: 'duration',
        });
      }

      const product = queryRunner.manager.create(Product, {
        name: parsed.itemDescription || `Imported Product - ${parsed.itemCode}`,
        price: parsed.price,
        type: ProductType.SERVICE, // Default to service type
        category: defaultCategory,
        duration: defaultDuration,
      });

      return await queryRunner.manager.save(Product, product);
    } catch (error) {
      this.logger.error(`Failed to create fallback product: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a default branch when no branches exist
   */
  private async createDefaultBranch(queryRunner: QueryRunner): Promise<Branch> {
    this.logger.log('Creating default HQ branch');

    try {
      // Get or create default status
      let activeStatus = await queryRunner.manager.findOne(Setting, {
        where: { name: 'Active', type: 'status' },
      });
      if (!activeStatus) {
        activeStatus = await queryRunner.manager.save(Setting, {
          name: 'Active',
          type: 'status',
        });
      }

      const branch = queryRunner.manager.create(Branch, {
        code: 'HQ',
        name: 'Headquarters',
        address: 'Default Address',
        status: activeStatus,
      });

      return await queryRunner.manager.save(Branch, branch);
    } catch (error) {
      this.logger.error(`Failed to create default branch: ${error.message}`);
      throw error;
    }
  }
}
