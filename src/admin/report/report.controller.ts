import {
  Controller,
  Get,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import {
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from 'src/core/crud/crud';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiOperation, ApiConsumes, ApiBody } from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { REPORT_ALIAS } from './report.const';
import { ReportService } from './report.service';
import { ReportInterceptor } from './report.interceptor';
import { response } from 'express';
import { Public } from '../auth/decorators/public.decerator';
import { CustomerPrepaidDetailImportService } from './report-customer/services/customer-prepaid-detail-import.service';

@Controller(REPORT_ALIAS)
@UseGuards(JwtAuthGuard)
@UseInterceptors(ReportInterceptor)
export class ReportController {
  constructor(
    public reportService: ReportService,
    private readonly importService: CustomerPrepaidDetailImportService,
  ) {}

  @Get('/')
  @UseInterceptors(CrudRequestInterceptor)
  async getReport(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: any,
  ) {
    return this.reportService.getReport(crudReq, req, query);
  }

  @Public()
  @Get('/export')
  @UseInterceptors(CrudRequestInterceptor)
  async exportReport(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: any,
    @Res() response,
  ) {
    const { filename, csv } = await this.reportService.getReport(
      crudReq,
      req,
      query,
      true,
    );
    // Set the response headers to trigger the file download
    response.setHeader('Content-Type', 'text/csv');
    response.attachment(filename);
    // // // Send the file as the response
    response.status(200).send(csv);
  }

  @Post('customer/import-prepaid-detail')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Import Customer Prepaid Detail from CSV' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'CSV file containing customer prepaid detail data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importCustomerPrepaidDetail(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('CSV file is required');
    }

    if (!file.originalname.toLowerCase().endsWith('.csv')) {
      throw new BadRequestException('Only CSV files are allowed');
    }

    if (file.size > 50 * 1024 * 1024) {
      // 50MB limit for large prepaid detail files
      throw new BadRequestException('File size must be less than 50MB');
    }

    try {
      const result = await this.importService.importFromCSV(file.buffer);
      return result;
    } catch (error) {
      throw new BadRequestException(`Import failed: ${error.message}`);
    }
  }
}
