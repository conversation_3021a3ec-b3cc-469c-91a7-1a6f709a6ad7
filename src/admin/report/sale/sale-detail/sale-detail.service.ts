import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import { PaymentMethod } from 'src/admin/payment-method/payment-method.entity';
import * as moment from 'moment-timezone';
import { ReportSaleDetailQueryDTO } from './sale-detail.dto';
import { InvoiceStatus, OrderType, ProductType } from 'src/core/enums/entity';
import Decimal from 'decimal.js';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { Order } from 'src/admin/order/order.entity';
import { Product } from 'src/admin/product/product.entity';
import { Employee } from 'src/admin/employee/employee.entity';
import { Like } from 'typeorm';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';

@Injectable()
export class ReportSaleDetailService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    @InjectRepository(Employee)
    private employeeRepo: Repository<Employee>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
      keySearch,
      customerReference,
    }: ReportSaleDetailQueryDTO,
    isExport = false,
  ) {
    const saleDetails = await this.getSaleDetails(
      startDate,
      endDate,
      branchIds,
      customerReference,
      clientZoneName,
      keySearch,
    );

    let sumQty = 0;
    let sumDiscount = 0;
    let sumCredit = 0;
    let sumTotal = 0;
    let sumPrepaid = 0;
    let sumFoc = 0;
    let sumSubTotal = 0;
    let sumDiscountAmount = 0;
    let sumTax = 0;
    let sumTotalAmount = 0;
    let sumReceived = 0;

    for (const detail of saleDetails) {
      sumQty += Number(detail.qty) || 0;
      sumDiscount += Number(detail.discount) || 0;
      sumCredit += Number(detail.credit) || 0;
      sumTotal += Number(detail.total) || 0;
      sumPrepaid += Number(detail.prepaid) || 0;
      sumFoc += Number(detail.foc) || 0;
      sumSubTotal += Number(detail.subtotal) || 0;
      sumDiscountAmount += Number(detail.discountAmount) || 0;
      sumTax += Number(detail.tax) || 0;
      sumTotalAmount += Number(detail.totalAmount) || 0;
      sumReceived += Number(detail.received) || 0;
    }

    if (isExport) {
      const grandTotal = this.calculateGrandTotal(saleDetails);
      return [...saleDetails, grandTotal];
    }

    return {
      data: saleDetails,
      sumQty: sumQty,
      sumDiscount: sumDiscount.toFixed(2),
      sumCredit: sumCredit.toFixed(2),
      sumTotal: sumTotal.toFixed(2),
      sumPrepaid: sumPrepaid.toFixed(2),
      sumFoc: sumFoc.toFixed(2),
      sumSubTotal: sumSubTotal.toFixed(2),
      sumDiscountAmount: sumDiscountAmount.toFixed(2),
      sumTax: sumTax.toFixed(2),
      sumTotalAmount: sumTotalAmount.toFixed(2),
      sumReceived: sumReceived.toFixed(2),
    };
  }

  private async getSaleDetails(
    startDate: string,
    endDate: string,
    branchIds: string[],
    customerReference?: string,
    clientZoneName?: string,
    keySearch?: string,
  ) {
    let normalizedStartDate = startDate;
    let normalizedEndDate = endDate;
    if (startDate && clientZoneName) {
      normalizedStartDate = moment
        .tz(startDate, clientZoneName)
        .startOf('day')
        .toISOString();
    }
    if (endDate && clientZoneName) {
      const end = moment.tz(endDate, clientZoneName);
      if (
        end.hour() === 0 &&
        end.minute() === 0 &&
        end.second() === 0 &&
        end.millisecond() === 0
      ) {
        normalizedEndDate = end.endOf('day').toISOString();
      } else {
        normalizedEndDate = end.toISOString();
      }
    }
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('orderDetail.employees', 'employee')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayment')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('invoicePayment.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.invoiceCoupon', 'invoiceCoupon')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate: normalizedStartDate,
        endDate: normalizedEndDate,
      })
      .andWhere('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (customerReference) {
      query = query.andWhere(
        '(customer.code LIKE :search OR invoice.code LIKE :search)',
        { search: `%${customerReference}%` },
      );
    }

    if (keySearch) {
      query = query.andWhere(
        '(CAST(customer.code AS TEXT) LIKE :search OR invoice.code LIKE :search OR customer.firstName LIKE :search OR customer.lastName LIKE :search)',
        { search: `%${keySearch}%` },
      );
    }
    const orderDetails = await this.processOrderDetails(query, clientZoneName);
    return orderDetails;
  }

  private async processOrderDetails(query, clientZoneName) {
    const orderDetails = await query
      .orderBy('invoice.date', 'ASC')
      .addOrderBy('invoice.id', 'ASC')
      .getMany();

    const saleDetails = [];
    let lineCount = 1;

    const groupedDetails = this.groupOrderDetailsByInvoice(orderDetails);

    const pushedRefundIds = new Set();

    for (const invoiceId of Object.keys(groupedDetails)) {
      const details = groupedDetails[invoiceId];
      const firstDetail = details[0];
      const invoice = firstDetail.order.invoice;
      const customer = invoice.customer;

      const allDetailDiscountZero = details.every(
        (d) => !d.discount || d.discount === 0,
      );
      const invoiceDiscount = invoice.discount || 0;
      const totalBeforeTax = details.reduce(
        (sum, d) => sum + (d.price || 0) * (d.quantity || 0),
        0,
      );

      const eligibleDetails = details.filter(
        (d) => d.product && !d.product.isNotApplyDiscount,
      );
      const eligibleTotalBeforeTax = eligibleDetails.reduce(
        (sum, d) => sum + (d.price || 0) * (d.quantity || 0),
        0,
      );

      const creditUtilisedMap = {};
      const creditUtilisedMeta = {};
      let hasCreditUtilised = false;
      const refundRows: any[] = [];
      const refundIdSet = new Set();
      const creditUtilisedRows: any[] = [];

      for (const detail of details) {
        // Skip service/product items that are part of a combo coupon
        if (
          detail.couponCode &&
          (detail.product?.type === ProductType.SERVICE ||
            detail.product?.type === ProductType.PRODUCT)
        ) {
          continue;
        }
        const type =
          detail.product?.type === ProductType.BEVERAGE ||
          detail.product?.type === ProductType.FOOD
            ? 'F&B'
            : this.determineProductType(detail);
        const unitPrice = detail.product?.price || 0;
        const quantity = detail.quantity || 0;

        const invoiceCoupon =
          Array.isArray(invoice.invoiceCoupon) &&
          invoice.invoiceCoupon.length > 0
            ? invoice.invoiceCoupon[0]
            : null;

        let unitDiscount = 0;
        let totalDiscount = 0;
        let totalPrice = 0;
        let totalCredit = 0;
        let lineTotalBeforeTax = 0;
        let taxBase = 0;
        let taxAmount = 0;
        let total = 0;

        if (type === 'U') {
          hasCreditUtilised = true;
          creditUtilisedRows.push({
            detail,
            formattedDate: moment(invoice.date)
              .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
              .format('DD/MM/YYYY hh:mm A'),
            invoice,
            customer,
            employeeName:
              detail.employees && detail.employees.length > 0
                ? detail.employees[0].fullName || ''
                : '',
          });
          continue;
        } else {
          if (allDetailDiscountZero && invoiceDiscount > 0 && unitPrice > 0) {
            if (detail.product && !detail.product.isNotApplyDiscount) {
              if (
                invoiceCoupon &&
                invoiceCoupon.couponType === 'Percentage' &&
                invoiceCoupon.percent
              ) {
                unitDiscount = +(
                  unitPrice *
                  (invoiceCoupon.percent / 100)
                ).toFixed(2);
              } else {
                unitDiscount = +(
                  (invoiceDiscount / eligibleTotalBeforeTax) *
                  unitPrice
                ).toFixed(2);
              }
            } else {
              unitDiscount = 0;
            }
          } else {
            unitDiscount = detail.discount || 0;
          }
          totalDiscount = +(unitDiscount * quantity).toFixed(2);
          totalPrice = +(unitPrice * quantity).toFixed(2);
          totalCredit = +(totalPrice - totalDiscount).toFixed(2);
          lineTotalBeforeTax = totalPrice;
          taxBase = totalPrice - totalDiscount;
          taxAmount = taxBase > 0 ? +(taxBase * 0.09).toFixed(2) : 0;
          // Update: Total should not include tax (Price - Discount)
          total = totalPrice - totalDiscount;
        }

        const paymentMethod = this.getPaymentMethod(invoice);

        const formattedDate = moment(invoice.date)
          .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
          .format('DD/MM/YYYY hh:mm A');

        let employeeName = '';
        if (detail.employees && detail.employees.length > 0) {
          employeeName = detail.employees[0].fullName || '';
        }

        // For coupon items (combo coupon), set price = 0.00 and all fields to 0 if type === 'CP'
        let displayPrice = unitPrice;
        let isComboCoupon = false;
        if (
          detail.product?.type === ProductType.COUPON &&
          detail.couponCode &&
          details.some(
            (d) =>
              d.couponCode &&
              d.couponCode === detail.couponCode &&
              d !== detail,
          )
        ) {
          displayPrice = 0;
          isComboCoupon = true;
        }

        if (type === 'CP' && isComboCoupon) {
          saleDetails.push({
            lineNumber: lineCount++,
            date: formattedDate,
            referenceNo: invoice.code ? `IN${invoice.code}` : '',
            customer: customer
              ? `${customer.code} ${customer.firstName} ${customer.lastName}`
              : '',
            employee: invoice.referral
              ? invoice.referral.username ||
                invoice.referral.displayName ||
                invoice.referral.fullname ||
                ''
              : '',
            item: detail.product ? detail.product.name : '',
            type: type,
            price: '0.00',
            qty: quantity,
            discount: '0.00',
            credit: '0.00',
            total: '0.00',
            prepaid: '0.00',
            foc: '0.00',
            subtotal: '0.00',
            discountAmount: '0.00',
            tax: '0.00',
            totalAmount: '0.00',
            received: '0.00',
            paymentMethod: paymentMethod,
          });
          continue;
        }

        if (paymentMethod === 'Credits') {
          const rootCreditHistory = await this.creditHistoryRepo.findOne({
            where: {
              invoice: { id: detail.order.invoice.id },
            },
            relations: ['membership', 'credit', 'invoice'],
          });

          saleDetails.push({
            lineNumber: lineCount++,
            date: formattedDate,
            referenceNo: invoice.code ? `IN${invoice.code}` : '',
            customer: customer
              ? `${customer.code} ${customer.firstName} ${customer.lastName}`
              : '',
            employee: invoice.referral
              ? invoice.referral.username ||
                invoice.referral.displayName ||
                invoice.referral.fullname ||
                ''
              : '',
            item: detail.product ? detail.product.name : '',
            type: type, // S/G/P
            price:
              type === 'U'
                ? (detail.price || 0).toFixed(2)
                : displayPrice.toFixed(2),
            qty: quantity,
            discount: totalDiscount.toFixed(2),
            credit: totalCredit.toFixed(2),
            // Update: For credit payments with types P, S, F&B, total should equal credit
            total:
              type === 'P' || type === 'S' || type === 'F&B'
                ? totalCredit.toFixed(2)
                : '0.00',
            prepaid: 'No',
            foc: 'No',
            subtotal: '0.00',
            discountAmount: '0.00',
            tax: '0.00',
            totalAmount:
              type === 'P' || type === 'S' || type === 'F&B'
                ? totalCredit.toFixed(2)
                : '0.00',
            received:
              type === 'P' || type === 'S' || type === 'F&B'
                ? totalCredit.toFixed(2)
                : '0.00',
            paymentMethod: paymentMethod,
          });

          const creditId = rootCreditHistory?.credit?.id || 'unknown';
          if (!creditUtilisedMap[creditId]) {
            creditUtilisedMap[creditId] = {
              price: 0,
              qty: 0,
              total: 0,
            };
            creditUtilisedMeta[creditId] = {
              date: formattedDate,
              referenceNo: invoice.code ? `IN${invoice.code}` : '',
              customer: customer
                ? `${customer.code} ${customer.firstName} ${customer.lastName}`
                : '',
              employee: invoice.referral
                ? invoice.referral.username ||
                  invoice.referral.displayName ||
                  invoice.referral.fullname ||
                  ''
                : '',
              item:
                rootCreditHistory?.membership?.detail?.['orders']?.[0]?.product
                  ?.name || (detail.product ? detail.product.name : ''),
              paymentMethod: paymentMethod,
            };
          }
          creditUtilisedMap[creditId].price += unitPrice - totalDiscount;
          creditUtilisedMap[creditId].qty += -Math.abs(quantity);
          creditUtilisedMap[creditId].total += -(unitPrice - totalDiscount);
          hasCreditUtilised = true;

          if (rootCreditHistory?.credit?.id) {
            const refundHistories = await this.creditHistoryRepo.find({
              where: {
                credit: { id: rootCreditHistory?.credit?.id },
                membership: { id: rootCreditHistory?.membership?.id },
                isRefund: true,
              },
              relations: ['membership'],
            });

            for (const refund of refundHistories) {
              if (refund.id && refundIdSet.has(refund.id)) continue;
              if (refund.id) refundIdSet.add(refund.id);
              refundRows.push({
                refundId: refund.id,
                lineNumber: null,
                date: formattedDate,
                referenceNo: rootCreditHistory?.membership?.detail
                  ? `CN${(rootCreditHistory?.membership?.detail as any).code}`
                  : '',
                customer: customer
                  ? `${customer.code} ${customer.firstName} ${customer.lastName}`
                  : '',
                employee: invoice.referral
                  ? invoice.referral.username ||
                    invoice.referral.displayName ||
                    invoice.referral.fullname ||
                    ''
                  : '',
                item:
                  refund.membership?.detail?.['orders']?.[0]?.product?.name ||
                  'Membership (Refunded)',
                type: 'CP(R)',
                price: (refund.paid || 0).toFixed(2),
                qty: -1,
                discount: '0.00',
                credit: '0.00',
                total: (-Math.abs(refund.paid || 0)).toFixed(2),
                prepaid: 'No',
                foc: 'No',
                subtotal: '0.00',
                discountAmount: '0.00',
                tax: '0.00',
                totalAmount: '0.00',
                received: '0.00',
                paymentMethod: 'Credits',
              });
            }
          }

          continue;
        }

        const creditRefundHistory = await this.creditHistoryRepo.findOne({
          where: {
            invoice: { id: detail.order.invoice.id },
            isRefunded: true,
          },
          relations: ['credit'],
        });

        let refundHistory = null;
        if (creditRefundHistory) {
          refundHistory = await this.creditHistoryRepo.findOne({
            where: {
              credit: { id: creditRefundHistory.credit.id },
              isRefund: true,
            },
            relations: ['product', 'invoice'],
          });
        }

        if (
          refundHistory &&
          ((detail.product && detail.product.type === ProductType.MEMBERSHIP) ||
            (refundHistory.detail &&
              refundHistory.detail.orders &&
              refundHistory.detail.orders[0]?.product?.type === 'membership'))
        ) {
          const refundDetail = refundHistory.detail || {};
          const referenceNo = refundDetail.code || invoice.code || '';
          const prefixedReferenceNo = referenceNo.startsWith('IN')
            ? referenceNo
            : `IN${referenceNo}`;
          const rowType = 'G';
          const totalValue = Math.abs(unitPrice).toFixed(2);
          saleDetails.push({
            lineNumber: lineCount++,
            date: formattedDate,
            referenceNo: prefixedReferenceNo,
            customer: customer
              ? `${customer.code} ${customer.firstName} ${customer.lastName}`
              : '',
            employee: invoice.referral
              ? invoice.referral.username ||
                invoice.referral.displayName ||
                invoice.referral.fullname ||
                ''
              : '',
            item:
              refundDetail.orders && refundDetail.orders[0]?.product?.name
                ? refundDetail.orders[0].product.name
                : detail.product
                ? detail.product.name
                : '',
            type: rowType,
            price:
              type === 'U'
                ? (detail.price || 0).toFixed(2)
                : displayPrice.toFixed(2),
            qty: quantity,
            discount: totalDiscount.toFixed(2),
            credit: '0.00',
            total: totalValue,
            prepaid: 'No',
            foc: 'No',
            subtotal: '0.00',
            discountAmount: '0.00',
            tax: '0.00',
            totalAmount: '0.00',
            received: '0.00',
            paymentMethod: paymentMethod,
          });
          continue;
        }

        saleDetails.push({
          lineNumber: lineCount++,
          date: formattedDate,
          referenceNo: invoice.code ? `IN${invoice.code}` : '',
          customer: customer
            ? `${customer.code} ${customer.firstName} ${customer.lastName}`
            : '',
          employee: invoice.referral
            ? invoice.referral.username ||
              invoice.referral.displayName ||
              invoice.referral.fullname ||
              ''
            : '',
          item: detail.product ? detail.product.name : '',
          type: type,
          price:
            type === 'U'
              ? (detail.price || 0).toFixed(2)
              : displayPrice.toFixed(2),
          qty: quantity,
          discount: totalDiscount.toFixed(2),
          credit:
            paymentMethod === 'Credits'
              ? type === 'U'
                ? Math.abs(lineTotalBeforeTax).toFixed(2)
                : totalCredit.toFixed(2)
              : '0.00',
          total: total.toFixed(2),
          prepaid: 'No',
          foc: 'No',
          subtotal: lineTotalBeforeTax.toFixed(2),
          discountAmount: totalDiscount.toFixed(2),
          tax: taxAmount.toFixed(2),
          totalAmount: (total + taxAmount).toFixed(2), // totalAmount includes tax
          received: (total + taxAmount).toFixed(2), // received includes tax
          paymentMethod: paymentMethod,
        });
      }

      if (hasCreditUtilised && creditUtilisedRows.length > 0) {
        let totalCredit = 0;
        for (const d of saleDetails) {
          if (
            d.referenceNo === (invoice.code ? `IN${invoice.code}` : '') &&
            d.type !== 'U'
          ) {
            totalCredit += Number(d.credit) || 0;
          }
        }
        for (const row of creditUtilisedRows) {
          saleDetails.push({
            lineNumber: lineCount++,
            date: row.formattedDate,
            referenceNo: row.invoice.code ? `IN${row.invoice.code}` : '',
            customer: row.customer
              ? `${row.customer.code} ${row.customer.firstName} ${row.customer.lastName}`
              : '',
            employee: row.employeeName,
            item: row.detail.product ? row.detail.product.name : '',
            type: 'U',
            price: totalCredit.toFixed(2),
            qty: -1,
            discount: '0.00',
            credit: '0.00',
            total: (-totalCredit).toFixed(2),
            prepaid: 'No',
            foc: 'No',
            subtotal: '0.00',
            discountAmount: '0.00',
            tax: '0.00',
            totalAmount: '0.00',
            received: '0.00',
            paymentMethod: 'Credits',
          });
        }
      }
      for (const row of refundRows) {
        if (row.refundId && !pushedRefundIds.has(row.refundId)) {
          row.lineNumber = lineCount++;
          saleDetails.push(row);
          pushedRefundIds.add(row.refundId);
        }
      }

      // After all details, always add a type U row if there are any non-U, Credits items for this invoice
      const creditRows = saleDetails.filter(
        (d) =>
          d.referenceNo === (invoice.code ? `IN${invoice.code}` : '') &&
          d.paymentMethod === 'Credits' &&
          d.type !== 'U',
      );
      if (creditRows.length > 0) {
        let totalCredit = 0;
        for (const d of creditRows) {
          totalCredit += Number(d.credit) || 0;
        }
        // Find the correct index to insert: after last non-U, non-CP(R) item for this invoice
        const refNo = invoice.code ? `IN${invoice.code}` : '';
        let insertIdx = -1;
        for (let i = 0; i < saleDetails.length; i++) {
          if (
            saleDetails[i].referenceNo === refNo &&
            saleDetails[i].type !== 'U' &&
            saleDetails[i].type !== 'CP(R)'
          ) {
            insertIdx = i;
          }
        }
        const uRow = {
          lineNumber: lineCount++,
          date: moment(invoice.date)
            .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
            .format('DD/MM/YYYY hh:mm A'),
          referenceNo: refNo,
          customer: customer
            ? `${customer.code} ${customer.firstName} ${customer.lastName}`
            : '',
          employee: '',
          item: 'Membership 1 day .',
          type: 'U',
          price: totalCredit.toFixed(2),
          qty: -1,
          discount: '0.00',
          credit: '0.00',
          total: (-totalCredit).toFixed(2),
          prepaid: 'No',
          foc: 'No',
          subtotal: '0.00',
          discountAmount: '0.00',
          tax: '0.00',
          totalAmount: '0.00',
          received: '0.00',
          paymentMethod: 'Credits',
        };
        if (insertIdx >= 0) {
          saleDetails.splice(insertIdx + 1, 0, uRow);
        } else {
          saleDetails.push(uRow);
        }
      }
    }

    return saleDetails;
  }

  private groupOrderDetailsByInvoice(orderDetails: OrderDetail[]) {
    const grouped: { [key: string]: OrderDetail[] } = {};

    for (const detail of orderDetails) {
      const invoiceId = detail.order.invoice.id;

      if (!grouped[invoiceId]) {
        grouped[invoiceId] = [];
      }

      grouped[invoiceId].push(detail);
    }

    return grouped;
  }

  private determineProductType(detail: OrderDetail): string {
    if (!detail.product) return '';

    switch (detail.product.type) {
      case ProductType.SERVICE:
        return 'S'; // Service
      case ProductType.COUPON:
        return detail.couponCode ? 'CP' : 'C'; // Coupon or Coupon redemption
      case ProductType.MEMBERSHIP:
        return 'G'; // Package
      case ProductType.PRODUCT:
        return 'P'; // Default to Retail Product
      case ProductType.FOOD_BEVERAGE:
        return 'FB'; // F&B
      default:
        if (detail.quantity && detail.quantity < 0) {
          return 'U'; // Credit Utilised
        }
        return 'S';
    }
  }

  private calculateDiscount(detail: OrderDetail, invoice: Invoice): number {
    const itemDiscount = detail.discount || 0;
    return itemDiscount;
  }

  private calculateTax(detail: OrderDetail, invoice: Invoice): number {
    const price = detail.price || 0;
    const quantity = detail.quantity || 0;
    const totalBeforeTax = price * quantity;

    const taxRate = invoice.tax ? invoice.tax / 100 : 0;

    return totalBeforeTax * taxRate;
  }

  private getPaymentMethod(invoice: Invoice): string {
    if (invoice.invoicePayments && invoice.invoicePayments.length > 0) {
      // Get all unique payment method names, join by comma
      const names = invoice.invoicePayments
        .map((pm) => pm.paymentMethod?.name)
        .filter(Boolean);
      return Array.from(new Set(names)).join(', ') || 'Unknown';
    }
    return 'Unknown';
  }

  private calculateGrandTotal(saleDetails: any[]) {
    let totalQty = 0;
    let totalDiscount = 0;
    let totalCredit = 0;
    let totalAmount = 0;
    let totalPrepaid = 0;
    let totalFoc = 0;
    let totalSubTotal = 0;
    let totalDiscountAmount = 0;
    let totalTax = 0;
    let totalTotal = 0;
    let totalReceived = 0;

    for (const detail of saleDetails) {
      totalQty += Number(detail.qty) || 0;
      totalDiscount += Number(detail.discount) || 0;
      totalCredit += Number(detail.credit) || 0;
      totalAmount += Number(detail.total) || 0;
      totalPrepaid += Number(detail.prepaid) || 0;
      totalFoc += Number(detail.foc) || 0;
      totalSubTotal += Number(detail.subtotal) || 0;
      totalDiscountAmount += Number(detail.discountAmount) || 0;
      totalTax += Number(detail.tax) || 0;
      totalTotal += Number(detail.totalAmount) || 0;
      totalReceived += Number(detail.received) || 0;
    }

    return {
      lineNumber: '',
      date: 'GRAND TOTAL:',
      referenceNo: '',
      customer: '',
      employee: '',
      item: '',
      type: '',
      price: '',
      qty: totalQty,
      discount: totalDiscount.toFixed(2),
      credit: totalCredit.toFixed(2),
      total: totalAmount.toFixed(2),
      prepaid: totalPrepaid.toFixed(2),
      foc: totalFoc.toFixed(2),
      subtotal: totalSubTotal.toFixed(2),
      discountAmount: totalDiscountAmount.toFixed(2),
      tax: totalTax.toFixed(2),
      totalAmount: totalTotal.toFixed(2),
      received: totalReceived.toFixed(2),
      paymentMethod: '',
    };
  }
}
