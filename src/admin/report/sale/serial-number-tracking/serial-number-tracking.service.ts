import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import * as moment from 'moment-timezone';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { SerialNumberTrackingQueryDTO } from './serial-number-tracking.dto';
import { InvoiceStatus, ProductType } from 'src/core/enums/entity';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { CouponItem } from 'src/admin/inventory/coupon-item.entity';

@Injectable()
export class SerialNumberTrackingService {
  private readonly logger = new Logger(SerialNumberTrackingService.name);

  constructor(
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(CouponItem)
    private couponItemRepo: Repository<CouponItem>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      purchaseStartDate,
      purchaseEndDate,
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      clientZoneName,
      searchText,
    }: SerialNumberTrackingQueryDTO,
    isExport = false,
  ) {
    const soldCoupons = await this.getSoldCoupons(
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const redeemedCoupons = await this.getRedeemedCoupons(
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const mergedData = this.mergeCouponData(soldCoupons, redeemedCoupons);

    if (isExport) {
      return [
        ...mergedData,
        {
          lineNumber: '',
          coupon: 'GRAND TOTAL:',
          serialNumber: '',
          soldDate: '',
          soldCustomer: '',
          soldReferenceCode: '',
          usedDate: '',
          usedCustomer: '',
          usedReferenceCode: '',
        },
      ];
    }

    return {
      data: mergedData,
      totalSold: soldCoupons.length,
      totalUsed: redeemedCoupons.length,
    };
  }

  private async getSoldCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('product.type = :productType', {
        productType: ProductType.COUPON,
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    const orderDetails = await query.getMany();

    const results = [];
    for (const detail of orderDetails) {
      const invoice = detail.order.invoice;
      if (!invoice) continue; // Sanity check

      const customer = invoice.customer;
      const formattedDate = moment(invoice.date)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A');

      let foundCouponItems = [];
      if (customer?.email) {
        // Find the corresponding CouponItem records assigned during this purchase
        foundCouponItems = await this.couponItemRepo
          .createQueryBuilder('couponItem')
          .leftJoin('couponItem.issueCoupon', 'issueCoupon')
          .leftJoin('issueCoupon.coupon', 'product')
          .where('product.id = :productId', { productId: detail.product.id })
          .andWhere('couponItem.email = :email', { email: customer.email })
          .andWhere('couponItem.startDate BETWEEN :startTime AND :endTime', {
            startTime: moment(invoice.date).subtract(2, 'minutes').toDate(),
            endTime: moment(invoice.date).add(2, 'minutes').toDate(),
          })
          .orderBy('couponItem.code', 'ASC')
          .getMany();
      }

      const itemsToReport =
        searchText && foundCouponItems.length > 0
          ? foundCouponItems.filter((item) => item.code.includes(searchText))
          : foundCouponItems;

      if (itemsToReport.length > 0) {
        for (const couponItem of itemsToReport) {
          results.push({
            lineNumber: results.length + 1,
            coupon: detail.product.name,
            serialNumber: couponItem.code, // <-- Correct serial number
            soldDate: formattedDate,
            soldCustomer: customer
              ? `${customer.code} ${customer.firstName} ${customer.lastName}`
              : '',
            soldReferenceCode: invoice.code ? `IN${invoice.code}` : '',
            usedDate: '',
            usedCustomer: '',
            usedReferenceCode: '',
          });
        }
      } else if (!searchText) {
        // Fallback for cases where no link is found (e.g., data inconsistency)
        // or for purchases that haven't been processed to assign a coupon yet.
        // We add one empty row to represent the sale, as per original logic.
        results.push({
          lineNumber: results.length + 1,
          coupon: detail.product.name,
          serialNumber: detail.couponCode || '', // Will be empty
          soldDate: formattedDate,
          soldCustomer: customer
            ? `${customer.code} ${customer.firstName} ${customer.lastName}`
            : '',
          soldReferenceCode: invoice.code ? `IN${invoice.code}` : '',
          usedDate: '',
          usedCustomer: '',
          usedReferenceCode: '',
        });
      }
    }

    return results;
  }

  private async getRedeemedCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    // Query 1: Get redemptions from OrderDetail.couponCode (legacy flow)
    let orderDetailQuery = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('orderDetail.couponCode IS NOT NULL')
      .andWhere('orderDetail.couponCode != :emptyString', { emptyString: '' })
      .andWhere('product.type != :productType', {
        productType: 'coupon',
      });

    if (branchIds && branchIds.length > 0) {
      orderDetailQuery = orderDetailQuery.andWhere(
        'branch.id IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    if (searchText) {
      orderDetailQuery = orderDetailQuery.andWhere(
        '(orderDetail.couponCode LIKE :search)',
        {
          search: `%${searchText}%`,
        },
      );
    }

    const orderDetails = await orderDetailQuery.getMany();

    // Query 2: Get redemptions from InvoiceCoupon table (current flow)
    let invoiceCouponQuery = this.invoiceRepo.manager
      .createQueryBuilder()
      .select([
        'invoiceCoupon.couponCode as couponCode',
        'invoice.date as invoiceDate',
        'invoice.code as invoiceCode',
        'customer.code as customerCode',
        'customer.firstName as customerFirstName',
        'customer.lastName as customerLastName',
        'branch.id as branchId',
      ])
      .from('invoice_coupon', 'invoiceCoupon')
      .leftJoin(
        'invoice_invoice_coupon_invoice_coupon',
        'junction',
        'junction.invoiceCouponId = invoiceCoupon.id',
      )
      .leftJoin('invoice', 'invoice', 'invoice.id = junction.invoiceId')
      .leftJoin('customer', 'customer', 'customer.id = invoice.customerId')
      .leftJoin('branch', 'branch', 'branch.id = invoice.branchId')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('invoiceCoupon.couponCode IS NOT NULL')
      .andWhere('invoiceCoupon.couponCode != :emptyString', { emptyString: '' })
      .andWhere('invoiceCoupon.couponType = :couponType', {
        couponType: 'Code',
      });

    if (branchIds && branchIds.length > 0) {
      invoiceCouponQuery = invoiceCouponQuery.andWhere(
        'branch.id IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    if (searchText) {
      invoiceCouponQuery = invoiceCouponQuery.andWhere(
        '(invoiceCoupon.couponCode LIKE :search)',
        {
          search: `%${searchText}%`,
        },
      );
    }

    const invoiceCoupons = await invoiceCouponQuery.getRawMany();

    // Combine results from both sources
    const result = [];

    // Add OrderDetail redemptions
    for (const detail of orderDetails) {
      result.push({
        couponCode: detail.couponCode,
        date: moment(detail.order.invoice.date)
          .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
          .format('DD/MM/YYYY hh:mm A'),
        customer: detail.order.invoice.customer
          ? `${detail.order.invoice.customer.code} ${detail.order.invoice.customer.firstName} ${detail.order.invoice.customer.lastName}`
          : '',
        referenceCode: detail.order.invoice.code
          ? `IN${detail.order.invoice.code}`
          : '',
      });
    }

    // Add InvoiceCoupon redemptions
    for (const coupon of invoiceCoupons) {
      result.push({
        couponCode: coupon.couponcode,
        date: moment(coupon.invoicedate)
          .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
          .format('DD/MM/YYYY hh:mm A'),
        customer:
          coupon.customercode &&
          coupon.customerfirstname &&
          coupon.customerlastname
            ? `${coupon.customercode} ${coupon.customerfirstname} ${coupon.customerlastname}`
            : '',
        referenceCode: coupon.invoicecode ? `IN${coupon.invoicecode}` : '',
      });
    }

    return result;
  }

  private mergeCouponData(soldCoupons: any[], redeemedCoupons: any[]) {
    return soldCoupons.map((sold) => {
      const possibleRedeemed = redeemedCoupons.filter(
        (r) => r.couponCode === sold.serialNumber,
      );

      const soldMoment = moment(sold.soldDate, 'DD/MM/YYYY hh:mm A');

      const redeemed = possibleRedeemed.find((r) => {
        const usedMoment = moment(r.date, 'DD/MM/YYYY hh:mm A');
        return usedMoment.isSameOrAfter(soldMoment);
      });

      return {
        ...sold,
        usedDate: redeemed?.date || '',
        usedCustomer: redeemed?.customer || '',
        usedReferenceCode: redeemed?.referenceCode || '',
      };
    });
  }
}
