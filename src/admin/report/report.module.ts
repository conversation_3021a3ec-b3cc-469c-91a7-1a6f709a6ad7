import { Module } from '@nestjs/common';
import { ReportController } from './report.controller';
import { ReportService } from './report.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from '../product/product.entity';
import { Category } from '../category/category.entity';
import { Customer } from '../customer/customer.entity';
import { Invoice } from '../invoice/invoice.entity';
import { ReportCustomerService } from './report-customer/report-customer.service';
import { ReportServiceService } from './report-service/report-service.service';
import { ReportSaleService } from './report-sale/report-sale.service';
import { Credit } from '../credit/credit.entity';
import { CreditHistory } from '../credit-history/credit-history.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Order } from '../order/order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { CouponItem } from '../inventory/coupon-item.entity';
import { ReportAppointmentService } from './report-appointment/report-appointment.service';
import { CreditSetting } from '../settings/credit/credit-setting.entity';
import { ReportCouponService } from './report-coupon/report-coupon.service';
import { ReportEmployeeSaleService } from './report-employee-sale-service/report-employee-sale.service';
import { UserSession } from '../user-session/user-session.entity';
import { ReportUserService } from './report-user/report-user.service';
import { PaymentMethod } from '../payment-method/payment-method.entity';
import { InvoicePaymentMethod } from '../invoice/invoice-payment-method.entity';
import { ReportDailySaleSummaryService } from './sale/daily-received-summary/daily-received-summary.service';
import { User } from '../user/user.entity';
import { ReportAnalyticsService } from './report-analytics/report-analytics.service';
import { AuditTrail } from '../audit-trail/audit-trail.entity';
import { Employee } from '../employee/employee.entity';
import { ReportReturnService } from './report-return/report-return.service';
import { MembershipHistory } from '../credit-history/membership-history.entity';
import { ReportDailyReceivedService } from './sale/daily-received/daily-received.service';
import { ReportSaleSummaryService } from './sale/sale-summary/sale-summary.service';
import { ReportCreditPurchaseService } from './prepaid-credit/credit-purchase/credit-purchase.service';
import { ReportCreditConsumptionService } from './prepaid-credit/credit-consumption/credit-consumption.service';
import { SalesModule } from '../sales/sales.module';
import { ReportPrepaidConsumptionService } from './prepaid-credit/prepaid-consumption/prepaid-consumption.service';
import { CommissionSettingModule } from '../settings/commission/commission-setting.module';
import { ReportRevenueConversionService } from './sale/revenue-conversion/revenue-conversion.service';
import { ReportSaleDetailService } from './sale/sale-detail/sale-detail.service';
import { ReportFocItemService } from './sale/foc-item/foc-item.service';
import { SerialNumberTrackingService } from './sale/serial-number-tracking/serial-number-tracking.service';
import { ReporProductService } from './report-product/report-product.service';
import { CustomerPrepaidDetailImportService } from './report-customer/services/customer-prepaid-detail-import.service';
import { Branch } from '../branch/branch.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      Category,
      Customer,
      Credit,
      CreditHistory,
      Appointment,
      Invoice,
      Order,
      OrderDetail,
      CouponItem,
      CreditSetting,
      UserSession,
      PaymentMethod,
      InvoicePaymentMethod,
      User,
      AuditTrail,
      Employee,
      MembershipHistory,
      Branch,
      CreditSetting,
    ]),
    SalesModule,
    CommissionSettingModule,
  ],
  providers: [
    ReportService,
    ReporProductService,
    ReportCustomerService,
    ReportServiceService,
    ReportSaleService,
    ReportAppointmentService,
    ReportCouponService,
    ReportEmployeeSaleService,
    ReportUserService,
    ReportDailySaleSummaryService,
    ReportAnalyticsService,
    ReportReturnService,
    ReportDailyReceivedService,
    ReportSaleSummaryService,
    ReportCreditPurchaseService,
    ReportCreditConsumptionService,
    ReportPrepaidConsumptionService,
    ReportRevenueConversionService,
    ReportSaleDetailService,
    ReportFocItemService,
    SerialNumberTrackingService,
    CustomerPrepaidDetailImportService,
  ],
  controllers: [ReportController],
  exports: [ReportService],
})
export class ReportModule {}
