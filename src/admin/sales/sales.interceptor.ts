import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  Logger,
} from '@nestjs/common';
import * as moment from 'moment-timezone';
import { Observable } from 'rxjs';
import {
  getTimeZoneNameFromOffset,
  UTCtimeZone,
} from 'src/core/common/common.utils';

@Injectable()
export class SalesInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SalesInterceptor.name);

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const headers = req.headers;
    const query = req.query;

    // Handle timezone extraction from query parameters
    // Priority: 1. Query startDate/endDate timezone offset, 2. Headers timezone, 3. Default UTC
    let clientZoneName = UTCtimeZone;

    try {
      // Try to extract timezone from date string offset (like report interceptor)
      const clientZone =
        query.startDate?.slice(-6) ||
        query.endDate?.slice(-6) ||
        headers['timezone'] ||
        headers['timezone-offset'] ||
        moment().format('Z');

      if (clientZone) {
        const extractedZoneName = getTimeZoneNameFromOffset(clientZone);
        if (extractedZoneName) {
          clientZoneName = extractedZoneName;
        }
      }

      this.logger.debug(
        `Extracted timezone: ${clientZoneName} from: ${clientZone}`,
      );
    } catch (error) {
      this.logger.warn(
        `Failed to extract timezone, using default UTC: ${error.message}`,
      );
      clientZoneName = UTCtimeZone;
    }

    // Set timezone in headers for service methods to access
    req.headers['timezone'] = clientZoneName;

    // Convert date range to proper timezone format for filtering
    if (query.startDate || query.endDate) {
      try {
        const startDate = query.startDate
          ? moment
              .tz(query.startDate, clientZoneName)
              .startOf('day')
              .toISOString()
          : moment().tz(clientZoneName).startOf('day').toISOString();

        const endDate = query.endDate
          ? moment.tz(query.endDate, clientZoneName).endOf('day').toISOString()
          : moment().tz(clientZoneName).endOf('day').toISOString();

        this.logger.debug(
          `Original dates: ${query.startDate} - ${query.endDate}`,
        );
        this.logger.debug(`Converted dates: ${startDate} - ${endDate}`);

        // Set normalized dates in query for CRUD parsing
        query.startDate = startDate;
        query.endDate = endDate;
      } catch (error) {
        this.logger.error(`Failed to convert dates: ${error.message}`);
        // Keep original dates if conversion fails
      }
    }

    // Set branchIds from headers (consistent with other interceptors)
    query.branchIds = headers?.['branchid']?.split(',') || [];

    return next.handle().pipe();
  }
}
