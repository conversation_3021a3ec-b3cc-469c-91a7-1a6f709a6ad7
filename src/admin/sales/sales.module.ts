import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { SalesController } from './sales.controller';
import { SalesService } from './sales.service';
import { Invoice } from '../invoice/invoice.entity';
import { Order } from '../order/order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { InvoicePaymentMethod } from '../invoice/invoice-payment-method.entity';
import { PaymentMethod } from '../payment-method/payment-method.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Customer } from '../customer/customer.entity';
import { Rfid } from '../rfid/rfid.entity';
import { SalesInterceptor } from './sales.interceptor';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Invoice,
      Order,
      OrderDetail,
      InvoicePaymentMethod,
      PaymentMethod,
      Appointment,
      Customer,
      Rfid,
    ]),
  ],
  controllers: [SalesController],
  providers: [SalesService, SalesInterceptor],
  exports: [SalesService],
})
export class SalesModule {}
