{"name": "onsen", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "NODE_OPTIONS=--max-old-space-size=8192 nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NODE_OPTIONS=--max-old-space-size=8192 nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "typeorm:mg-run": "npm run build && npm run typeorm migration:run -- -d ./src/core/database/typeorm.config.ts", "typeorm:mg-gen": "npm run build && npm run typeorm -- -d ./src/core/database/typeorm.config.ts migration:generate ./migrations/$npm_config_name", "typeorm:mg-create": "npm run typeorm -- migration:create ./migrations/$npm_config_name", "typeorm:mg-revert": "npm run typeorm -- -d ./src/core/database/typeorm.config.ts migration:revert"}, "dependencies": {"@nestjs/common": "^9.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^9.0.0", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^7.1.13", "@nestjs/typeorm": "^10.0.0", "@sendgrid/mail": "^7.7.0", "@types/multer": "^1.4.11", "@types/passport-jwt": "^3.0.10", "@zmotivat0r/o0": "^1.0.2", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cloudinary": "^1.41.0", "csv-parser": "^3.2.0", "decimal.js": "^10.4.3", "handlebars": "^4.7.8", "json2csv": "^6.0.0-alpha.2", "lodash": "^4.17.21", "mjml": "^4.15.3", "moment": "^2.29.4", "moment-timezone": "^0.5.44", "nodemailer": "^6.10.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.3", "pluralize": "^8.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "streamifier": "^0.1.1", "tencentcloud-sdk-nodejs": "^4.0.911", "typeorm": "^0.3.17", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.5.0", "@types/node": "18.15.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}