import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveUniqueDisplaynameIndex1753332837707
  implements MigrationInterface
{
  name = 'RemoveUniqueDisplaynameIndex1753332837707';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."unique_displayname_index"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."IDX_81afb288b526f7e8fed0e4200c"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_displayname_index" ON "employee" ("branchId", "displayName", "email") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_81afb288b526f7e8fed0e4200c" ON "employee" ("phone")`,
    );
  }
}
