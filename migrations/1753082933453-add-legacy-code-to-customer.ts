import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLegacyCodeToCustomer1753082933453
  implements MigrationInterface
{
  name = 'AddLegacyCodeToCustomer1753082933453';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "legacyCode" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "UQ_b04e967e17d40f9d1089ea7a125" UNIQUE ("legacyCode")`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_dc5912edb8a02da5e0b9b57c35"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_4c58be1b83ae68eece02e51964"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "legacyCode"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_dc5912edb8a02da5e0b9b57c35" ON "customer" ("phone") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_4c58be1b83ae68eece02e51964" ON "customer" ("email") WHERE deleted IS NULL`,
    );
  }
}
