import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOldOrderToInvoice1742887806793 implements MigrationInterface {
  name = 'AddOldOrderToInvoice1742887806793';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" ADD "oldOrder" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "oldOrder"`);
  }
}
