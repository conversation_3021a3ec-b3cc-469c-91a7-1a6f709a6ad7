// Simple test to verify the credit purchase API works
const axios = require('axios');

async function testCreditPurchaseAPI() {
  try {
    console.log('Testing Credit Purchase API...');
    
    // Test the API endpoint directly (without auth for now)
    const response = await axios.get('http://localhost:3100/', {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Server is running:', response.data);
    
    // Test if we can access the report endpoint (will get 401 but that's expected)
    try {
      const reportResponse = await axios.get('http://localhost:3100/report', {
        params: {
          reportType: 'report-credit-purchase',
          startDate: '2025-06-30T17:00:00.000Z',
          endDate: '2025-07-31T16:59:59.000Z',
          clientZoneName: 'Asia/Singapore',
          page: 1,
          limit: 10
        },
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Report API response:', reportResponse.data);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Report API is accessible (401 Unauthorized as expected without auth)');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

// Run the test
testCreditPurchaseAPI();
